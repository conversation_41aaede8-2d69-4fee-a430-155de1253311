<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Binance Signal Generator Pro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            padding: 20px;
        }
        .test-result {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success { border-color: #10b981; }
        .error { border-color: #ef4444; }
        button {
            background: #00ffff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8b5cf6;
            color: #fff;
        }
        pre {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🚀 Binance Signal Generator Pro - API Test</h1>
    
    <div>
        <button onclick="testStatus()">Test Status API</button>
        <button onclick="testSignals()">Test Signals API</button>
        <button onclick="testPairs()">Test Pairs API</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        async function testAPI(endpoint, description) {
            const resultsDiv = document.getElementById('results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-result';
            testDiv.innerHTML = `<h3>Testing: ${description}</h3><p>Loading...</p>`;
            resultsDiv.appendChild(testDiv);
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    testDiv.className = 'test-result success';
                    testDiv.innerHTML = `
                        <h3>✅ ${description} - SUCCESS</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                testDiv.className = 'test-result error';
                testDiv.innerHTML = `
                    <h3>❌ ${description} - ERROR</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        function testStatus() {
            testAPI('/api/status', 'Status API');
        }
        
        function testSignals() {
            testAPI('/api/signals?limit=5', 'Signals API (5 signals)');
        }
        
        function testPairs() {
            testAPI('/api/pairs?limit=10', 'Pairs API (10 pairs)');
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-test on load
        window.onload = function() {
            setTimeout(() => {
                testStatus();
                setTimeout(() => testSignals(), 1000);
                setTimeout(() => testPairs(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
