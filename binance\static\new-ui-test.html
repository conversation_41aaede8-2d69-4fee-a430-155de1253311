<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance Signal Generator Pro - New UI Test</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- New CSS -->
    <link rel="stylesheet" href="/static/css/new-style.css">

    <!-- Chart.js with Professional Trading Plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3.0.1/dist/chartjs-plugin-annotation.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial@0.2.1/dist/chartjs-chart-financial.min.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
</head>
<body>
    <div class="app-container">
        <div class="main-wrapper">
            <!-- Header -->
            <header class="app-header">
                <h1 class="app-title">🚀 Binance Signal Generator Pro</h1>
                <p class="app-subtitle">Advanced Trading Signal Analysis with AI-Powered Insights</p>
            </header>
            
            <!-- Navigation Tabs -->
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="signals">
                    📊 Trading Signals
                </button>
                <button class="nav-tab" data-tab="analysis">
                    🔍 Manual Analysis
                </button>
                <button class="nav-tab" data-tab="settings">
                    ⚙️ Settings
                </button>
                <button class="nav-tab" data-tab="about">
                    ℹ️ About
                </button>
            </nav>
            
            <!-- Main Content Area -->
            <main class="content-area">
                <!-- Trading Signals Tab -->
                <div id="signals-tab" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">📈</span>
                                Latest Trading Signals
                            </h2>
                            <button id="refresh-signals" class="btn btn-primary">
                                <span>🔄</span>
                                Refresh
                            </button>
                        </div>

                        <!-- Signal Counter -->
                        <div id="signal-counter" class="signal-counter">
                            Showing 10 of 487 total opportunities
                        </div>

                        <!-- Signals Container -->
                        <div id="signals-container" class="signals-grid">
                            <!-- Test Signal Card -->
                            <div class="signal-card card">
                                <div class="signal-header">
                                    <div class="signal-symbol">BTCUSDT</div>
                                    <div class="signal-type strong-buy">STRONG BUY</div>
                                </div>
                                <div class="signal-metrics">
                                    <div class="metric">
                                        <span class="metric-label">Confidence</span>
                                        <span class="metric-value high-confidence">85%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">Risk Level</span>
                                        <span class="metric-value">Medium</span>
                                    </div>
                                </div>
                                <div class="signal-prices">
                                    <div class="price-item">
                                        <span class="price-label">Entry</span>
                                        <span class="price-value">43250.50</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">Stop Loss</span>
                                        <span class="price-value">42800.00</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">Take Profit</span>
                                        <span class="price-value">44500.00</span>
                                    </div>
                                </div>
                                <div class="signal-reasoning">
                                    Strong bullish momentum with RSI oversold recovery and volume confirmation. Multiple support levels holding.
                                </div>
                                <div class="signal-timestamp">
                                    2025-07-13 12:30:00
                                </div>
                                <button class="ai-analysis-btn" data-symbol="BTCUSDT">
                                    <span class="btn-icon">🤖</span>
                                    <span class="btn-text">Analisis AI</span>
                                </button>
                            </div>
                            
                            <!-- Another Test Signal Card -->
                            <div class="signal-card card">
                                <div class="signal-header">
                                    <div class="signal-symbol">ETHUSDT</div>
                                    <div class="signal-type buy">BUY</div>
                                </div>
                                <div class="signal-metrics">
                                    <div class="metric">
                                        <span class="metric-label">Confidence</span>
                                        <span class="metric-value medium-confidence">72%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">Risk Level</span>
                                        <span class="metric-value">Low</span>
                                    </div>
                                </div>
                                <div class="signal-prices">
                                    <div class="price-item">
                                        <span class="price-label">Entry</span>
                                        <span class="price-value">2650.75</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">Stop Loss</span>
                                        <span class="price-value">2580.00</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">Take Profit</span>
                                        <span class="price-value">2750.00</span>
                                    </div>
                                </div>
                                <div class="signal-reasoning">
                                    Positive divergence on MACD with ascending triangle pattern formation. Good risk-reward ratio.
                                </div>
                                <div class="signal-timestamp">
                                    2025-07-13 12:25:00
                                </div>
                                <button class="ai-analysis-btn" data-symbol="ETHUSDT">
                                    <span class="btn-icon">🤖</span>
                                    <span class="btn-text">Analisis AI</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Analysis Tab -->
                <div id="analysis-tab" class="tab-content">
                    <!-- Manual Analysis Panel -->
                    <div class="manual-analysis-panel">
                        <div class="analysis-header">
                            <h2 class="analysis-title">
                                <span class="icon">🤖</span>
                                Manual Analysis Control
                            </h2>
                            <div class="status-badge idle">
                                <span class="status-indicator"></span>
                                Ready for analysis
                            </div>
                        </div>
                        
                        <div class="analysis-controls">
                            <div class="control-row">
                                <button id="start-analysis-btn" class="btn start-analysis-btn">
                                    <span class="btn-icon">🚀</span>
                                    <span class="btn-text">Start Analysis</span>
                                </button>
                                
                                <div class="analysis-info">
                                    <div class="info-item">
                                        <span class="info-label">Total Pairs</span>
                                        <span class="info-value" id="total-pairs">487</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Signals Generated</span>
                                        <span class="info-value" id="signals-generated">888</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Last Update</span>
                                        <span class="info-value" id="last-update">12:20:32</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Container -->
                        <div id="progress-container" class="progress-container">
                            <div class="progress-header">
                                <span id="progress-text" class="progress-text">Analyzing 250/487 pairs</span>
                                <span id="progress-percentage" class="progress-percentage">51%</span>
                            </div>
                            <div class="progress-bar">
                                <div id="progress-fill" class="progress-fill" style="width: 51%"></div>
                            </div>
                            <div class="progress-details">
                                <span>Current: <span id="current-pair">BTCUSDT</span></span>
                                <span id="estimated-time">~2:30 remaining</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Analysis Statistics -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <span class="icon">📊</span>
                                Analysis Statistics
                            </h3>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-icon">🌐</div>
                                <div class="stat-content">
                                    <div class="stat-label">Binance API</div>
                                    <div class="stat-value">🟢 Healthy</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">🤖</div>
                                <div class="stat-content">
                                    <div class="stat-label">AI Service</div>
                                    <div class="stat-value">🟡 Limited</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">⏱️</div>
                                <div class="stat-content">
                                    <div class="stat-label">Avg Analysis Time</div>
                                    <div class="stat-value">~4 min</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">🎯</div>
                                <div class="stat-content">
                                    <div class="stat-label">Success Rate</div>
                                    <div class="stat-value">85%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div id="settings-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">⚙️</span>
                                Application Settings
                            </h2>
                        </div>
                        
                        <div class="settings-content">
                            <!-- Gemini AI Configuration -->
                            <div class="api-config-section">
                                <div class="api-config-header">
                                    <h3 class="api-config-title">🤖 Konfigurasi Gemini 2.0 Flash AI</h3>
                                    <div class="api-status-indicator disconnected" id="api-status-indicator">
                                        <span class="status-dot">🔴</span>
                                        <span class="status-text">Disconnected</span>
                                    </div>
                                </div>

                                <div class="api-input-group">
                                    <div class="api-input-field">
                                        <label for="gemini-api-key">Gemini API Key</label>
                                        <input
                                            type="password"
                                            id="gemini-api-key"
                                            placeholder="Masukkan Gemini API Key Anda"
                                            autocomplete="off"
                                        >
                                    </div>
                                    <button class="verify-api-btn" id="verify-api-btn">
                                        <span class="btn-icon">🔍</span>
                                        <span class="btn-text">Verifikasi API</span>
                                    </button>
                                </div>

                                <div class="api-config-info">
                                    <p style="font-size: 0.85rem; color: var(--text-muted); margin: 0;">
                                        💡 API Key akan disimpan secara lokal dan digunakan untuk analisis AI mendalam dengan Gemini 2.0 Flash pada trading signals.
                                        <br>
                                        🔗 Dapatkan API Key gratis di: <a href="https://makersuite.google.com/app/apikey" target="_blank" style="color: var(--pastel-purple);">Google AI Studio</a>
                                        <br>
                                        ⚡ Menggunakan model terbaru Gemini 2.0 Flash untuk analisis yang lebih cepat dan akurat.
                                    </p>
                                </div>
                            </div>

                            <div class="setting-group">
                                <h3>Analysis Configuration</h3>
                                <div class="setting-item">
                                    <label>Auto-refresh interval (seconds)</label>
                                    <input type="number" value="30" min="10" max="300">
                                </div>
                                <div class="setting-item">
                                    <label>Maximum signals to display: <span id="signal-limit-value">10</span></label>
                                    <input type="range" id="signal-limit-slider" min="5" max="50" value="10" step="1" class="config-slider">
                                </div>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" id="enable-ai-analysis" checked>
                                        Enable AI Analysis on Signal Cards
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Notification Preferences</h3>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" checked>
                                        Show analysis completion notifications
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" checked>
                                        Show error notifications
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- About Tab -->
                <div id="about-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">ℹ️</span>
                                About Binance Signal Generator Pro
                            </h2>
                        </div>
                        
                        <div class="about-content">
                            <div class="about-section">
                                <h3>🚀 Features</h3>
                                <ul>
                                    <li>Real-time trading signal analysis</li>
                                    <li>AI-powered market insights</li>
                                    <li>Manual analysis control</li>
                                    <li>Beautiful, responsive UI</li>
                                    <li>Comprehensive progress tracking</li>
                                </ul>
                            </div>
                            
                            <div class="about-section">
                                <h3>👨‍💻 Developer</h3>
                                <p><strong>BOBACHEESE</strong></p>
                                <div class="social-links">
                                    <a href="#" class="social-link">🐙 GitHub</a>
                                    <a href="#" class="social-link">🐦 Twitter</a>
                                    <a href="#" class="social-link">💼 LinkedIn</a>
                                </div>
                            </div>
                            
                            <div class="about-section">
                                <h3>📝 Version Info</h3>
                                <p>Version: 2.0.0 (New UI)</p>
                                <p>Last Updated: 2025-07-13</p>
                                <p>Built with: Flask, Python, Modern CSS, Vanilla JS</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- AI Analysis Modal -->
    <div class="ai-analysis-modal" id="ai-analysis-modal">
        <div class="ai-analysis-content">
            <div class="ai-analysis-header">
                <h3 class="ai-analysis-title" id="ai-analysis-title">
                    🤖 Analisis AI - Loading...
                </h3>
                <button class="close-modal-btn" id="close-modal-btn">×</button>
            </div>
            <div class="ai-analysis-body">
                <div class="ai-analysis-loading" id="ai-analysis-loading">
                    <div class="loading-spinner"></div>
                    <p>Menganalisis data dengan Gemini 2.0 Flash AI...</p>
                    <p style="font-size: 0.85rem; color: var(--text-muted);">
                        Sedang memproses 60+ indikator teknis dan data real-time dengan model terbaru
                    </p>
                </div>
                <div class="ai-analysis-result" id="ai-analysis-result" style="display: none;">
                    <!-- AI analysis result will be displayed here -->
                </div>

                <!-- Chart Container -->
                <div class="chart-container" id="chart-container" style="display: none;">
                    <div class="chart-header">
                        <h4 class="chart-title">
                            <span class="icon">📈</span>
                            <span id="chart-symbol">Chart Analysis</span>
                        </h4>
                        <div class="chart-controls">
                            <div class="timeframe-selector">
                                <button class="timeframe-btn" data-timeframe="1m">1m</button>
                                <button class="timeframe-btn" data-timeframe="5m">5m</button>
                                <button class="timeframe-btn" data-timeframe="15m">15m</button>
                                <button class="timeframe-btn active" data-timeframe="1h">1h</button>
                                <button class="timeframe-btn" data-timeframe="4h">4h</button>
                                <button class="timeframe-btn" data-timeframe="1d">1d</button>
                            </div>
                            <button class="chart-toggle-btn" id="ai-overlay-toggle">
                                <span>🤖</span>
                                <span>AI Overlay</span>
                            </button>
                            <button class="fullscreen-btn" id="chart-fullscreen-btn" title="Fullscreen">
                                ⛶
                            </button>
                        </div>
                    </div>

                    <div class="chart-canvas-container">
                        <div class="chart-loading" id="chart-loading">
                            <div class="loading-spinner"></div>
                            <p>Loading chart data...</p>
                        </div>
                        <canvas id="trading-chart" style="display: none;"></canvas>
                        <div class="chart-error" id="chart-error" style="display: none;">
                            <div class="error-icon">📊</div>
                            <h4>Chart Error</h4>
                            <p>Failed to load chart data</p>
                        </div>
                    </div>

                    <div class="ai-overlay-controls" id="ai-overlay-controls" style="display: none;">
                        <div class="ai-overlay-toggle">
                            <input type="checkbox" id="show-entry-points" checked>
                            <label for="show-entry-points">Entry Points</label>
                        </div>
                        <div class="ai-overlay-toggle">
                            <input type="checkbox" id="show-stop-loss" checked>
                            <label for="show-stop-loss">Stop Loss</label>
                        </div>
                        <div class="ai-overlay-toggle">
                            <input type="checkbox" id="show-take-profit" checked>
                            <label for="show-take-profit">Take Profit</label>
                        </div>
                        <div class="ai-overlay-toggle">
                            <input type="checkbox" id="show-prediction" checked>
                            <label for="show-prediction">Prediction</label>
                        </div>
                    </div>

                    <div class="ai-overlay-legend">
                        <div class="legend-item">
                            <div class="legend-color entry"></div>
                            <span>AI Entry</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color stop-loss"></div>
                            <span>AI Stop Loss</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color take-profit"></div>
                            <span>AI Take Profit</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color prediction"></div>
                            <span>AI Prediction</span>
                        </div>

                        <!-- Trading Simulator Integration -->
                        <div class="trading-simulator-container" id="trading-simulator-container" style="display: none;">
                            <div class="simulator-header">
                                <h4>📊 Trading Simulator</h4>
                                <button class="simulator-toggle-btn" id="simulator-toggle-btn">
                                    <span>📈</span>
                                    <span>Toggle Simulator</span>
                                </button>
                            </div>

                            <div class="simulator-content" id="simulator-content">
                                <div class="simulator-row">
                                    <div class="simulator-section">
                                        <h5>Order Configuration</h5>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Order Type:</label>
                                                <select id="order-type" class="form-control">
                                                    <option value="market">Market</option>
                                                    <option value="limit">Limit</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>Quantity:</label>
                                                <input type="number" id="quantity" class="form-control" min="0.001" step="0.001" placeholder="0.001">
                                                <div class="input-tooltip">Min: 0.001, Max: 1000</div>
                                                <div class="form-validation-message" id="quantity-validation"></div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Leverage: <span id="leverage-value">1x</span></label>
                                                <input type="range" id="leverage" min="1" max="20" value="1" class="form-control">
                                                <div class="input-tooltip">Range: 1x - 20x leverage</div>
                                                <div class="form-validation-message" id="leverage-validation"></div>
                                            </div>
                                            <div class="form-group">
                                                <label>Position:</label>
                                                <div class="radio-group">
                                                    <label><input type="radio" name="position" value="long" checked> Long</label>
                                                    <label><input type="radio" name="position" value="short"> Short</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="simulator-section">
                                        <h5>Position Summary</h5>
                                        <div class="position-info">
                                            <div class="info-item">
                                                <span>Entry Price:</span>
                                                <span id="entry-price">$0.00</span>
                                            </div>
                                            <div class="info-item">
                                                <span>Current Price:</span>
                                                <span id="current-price">$0.00</span>
                                            </div>
                                            <div class="info-item">
                                                <span>Unrealized P&L:</span>
                                                <span id="unrealized-pnl">$0.00</span>
                                            </div>
                                            <div class="info-item">
                                                <span>ROE:</span>
                                                <span id="roe-percentage">0.00%</span>
                                            </div>
                                            <div class="info-item">
                                                <span>Margin Used:</span>
                                                <span id="margin-used">$0.00</span>
                                            </div>
                                        </div>

                                        <div class="simulator-actions">
                                            <button id="open-position" class="btn btn-success">Open Position</button>
                                            <button id="close-position" class="btn btn-danger" disabled>Close Position</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New JavaScript -->
    <script src="/static/js/new-app.js"></script>
    
    <!-- Test Script -->
    <script>
        // Show progress container for demo
        setTimeout(() => {
            document.getElementById('progress-container').classList.add('show');
        }, 2000);

        // Demo notification
        setTimeout(() => {
            if (window.app && window.app.showNotification) {
                window.app.showNotification('New UI with AI Analysis loaded successfully! 🎉', 'success');
            }
        }, 3000);

        // Demo Gemini 2.0 Flash API notification
        setTimeout(() => {
            if (window.app && window.app.showNotification) {
                window.app.showNotification('⚡ Gemini 2.0 Flash AI Analysis ready! Configure API in Settings.', 'info');
            }
        }, 5000);

        // Demo chart notification
        setTimeout(() => {
            if (window.app && window.app.showNotification) {
                window.app.showNotification('📈 Real-time Chart Integration ready! Click AI Analysis to see charts.', 'info');
            }
        }, 7000);

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // ESC to close modal
            if (e.key === 'Escape') {
                const modal = document.getElementById('ai-analysis-modal');
                if (modal && modal.classList.contains('show')) {
                    window.app.closeAIAnalysisModal();
                }
            }

            // Ctrl+Shift+A to test AI analysis
            if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                if (window.app) {
                    window.app.showNotification('🤖 Testing AI Analysis with Chart for BTCUSDT...', 'info');
                    setTimeout(() => {
                        window.app.startAIAnalysis('BTCUSDT');
                    }, 1000);
                }
            }

            // Ctrl+Shift+C to test chart only
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                if (window.app) {
                    window.app.showNotification('📈 Testing Chart for ETHUSDT...', 'info');
                    setTimeout(() => {
                        window.app.showChartForSymbol('ETHUSDT');
                    }, 1000);
                }
            }
        });

        // Add demo data update
        setInterval(() => {
            // Update some demo values
            const totalPairs = document.getElementById('total-pairs');
            const signalsGenerated = document.getElementById('signals-generated');
            const lastUpdate = document.getElementById('last-update');

            if (totalPairs) {
                totalPairs.textContent = Math.floor(Math.random() * 50) + 450;
            }
            if (signalsGenerated) {
                signalsGenerated.textContent = Math.floor(Math.random() * 100) + 800;
            }
            if (lastUpdate) {
                lastUpdate.textContent = new Date().toLocaleTimeString();
            }
        }, 30000); // Update every 30 seconds
    </script>
</body>
</html>
