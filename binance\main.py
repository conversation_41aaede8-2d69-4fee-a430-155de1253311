#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance Signal Prompt Generator Pro
Real-time Technical Analysis & AI-Powered Trading Signals

Author: BOBACHEESE
Version: 1.0.0
Framework: Flask + HTML/CSS/JavaScript + Chart.js
Target: 500+ Binance futures pairs, 60+ technical indicators, AI integration
Port: 5001 (untuk menghindari konflik dengan existing arbitrage bot)
"""

import os
import sys
import logging
import time
import threading
import json
import asyncio
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import traceback

# Suppress non-critical warnings
warnings.filterwarnings('ignore', category=RuntimeWarning, module='ta.trend')
warnings.filterwarnings('ignore', message='invalid value encountered in scalar divide')
warnings.filterwarnings('ignore', message='Connection pool is full')
warnings.filterwarnings('ignore', category=UserWarning, module='urllib3')

# Core dependencies
import requests
import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request, send_from_directory

# CORS import with fallback
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    print("Warning: flask_cors not available. CORS will be handled manually.")
    CORS_AVAILABLE = False

# Optional dependencies
try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    print("Warning: google.generativeai not available. AI features will be limited.")
    GENAI_AVAILABLE = False

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not available. Using environment variables directly.")

# Technical Analysis
import ta
from ta.trend import SMAIndicator, EMAIndicator, MACD, ADXIndicator, PSARIndicator
from ta.momentum import RSIIndicator, StochasticOscillator, WilliamsRIndicator, ROCIndicator, StochRSIIndicator
from ta.volatility import BollingerBands, AverageTrueRange, KeltnerChannel, DonchianChannel
from ta.volume import OnBalanceVolumeIndicator, VolumePriceTrendIndicator, AccDistIndexIndicator, ChaikinMoneyFlowIndicator

# Load environment variables
load_dotenv()

# Konfigurasi Logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.getenv('LOG_FILE', 'binance_signals.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global Configuration
CONFIG = {
    'binance': {
        'base_url': os.getenv('BINANCE_BASE_URL', 'https://fapi.binance.com'),
        'rate_limit': int(os.getenv('BINANCE_RATE_LIMIT', '1200')),  # requests per minute
        'timeout': int(os.getenv('BINANCE_TIMEOUT', '10')),
        'headers': {
            'User-Agent': 'Binance-Signal-Generator-Pro/1.0',
            'Content-Type': 'application/json'
        }
    },
    'performance': {
        'max_workers': int(os.getenv('MAX_WORKERS', '30')),  # Increased for 500+ pairs
        'cache_expiry': int(os.getenv('CACHE_EXPIRY', '300')),  # 5 minutes
        'max_memory_usage': int(os.getenv('MAX_MEMORY_USAGE', '2048')),  # MB
        'target_pairs': int(os.getenv('TARGET_PAIRS', '500')),
        'batch_size': int(os.getenv('BATCH_SIZE', '50')),  # Process in batches
        'analysis_timeout': int(os.getenv('ANALYSIS_TIMEOUT', '300'))  # 5 minutes max
    },
    'ai': {
        'api_key': os.getenv('GEMINI_API_KEY'),
        'model': 'gemini-pro',
        'rate_limit': 60,  # requests per minute
        'timeout': 30
    },
    'ui': {
        'theme': os.getenv('UI_THEME', 'dark_futuristic'),
        'language': os.getenv('UI_LANGUAGE', 'id'),
        'auto_refresh': os.getenv('AUTO_REFRESH', 'True').lower() == 'true',
        'notification_threshold': float(os.getenv('NOTIFICATION_THRESHOLD', '5.0'))
    }
}

@dataclass
class TradingPair:
    """Data class untuk trading pair information"""
    symbol: str
    base_asset: str
    quote_asset: str
    price: float
    volume_24h: float
    change_24h: float
    high_24h: float
    low_24h: float
    market_cap: Optional[float] = None
    last_update: Optional[datetime] = None

@dataclass
class CandlestickData:
    """Data class untuk candlestick data"""
    open_time: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    close_time: datetime
    quote_volume: float
    trades_count: int

@dataclass
class TechnicalIndicators:
    """Data class untuk technical indicators"""
    # Trend Indicators
    sma_9: float = 0.0
    sma_21: float = 0.0
    sma_50: float = 0.0
    sma_200: float = 0.0
    ema_9: float = 0.0
    ema_21: float = 0.0
    ema_50: float = 0.0
    ema_200: float = 0.0
    macd_line: float = 0.0
    macd_signal: float = 0.0
    macd_histogram: float = 0.0
    adx: float = 0.0
    psar: float = 0.0
    
    # Momentum Indicators
    rsi: float = 0.0
    stoch_k: float = 0.0
    stoch_d: float = 0.0
    williams_r: float = 0.0
    cci: float = 0.0
    roc: float = 0.0
    stoch_rsi: float = 0.0
    
    # Volatility Indicators
    bb_upper: float = 0.0
    bb_middle: float = 0.0
    bb_lower: float = 0.0
    atr: float = 0.0
    keltner_upper: float = 0.0
    keltner_middle: float = 0.0
    keltner_lower: float = 0.0
    
    # Volume Indicators
    obv: float = 0.0
    vpt: float = 0.0
    adi: float = 0.0
    cmf: float = 0.0
    
    # Support/Resistance
    support_level: float = 0.0
    resistance_level: float = 0.0
    pivot_point: float = 0.0
    
    # Pattern Recognition
    detected_patterns: List[str] = None
    pattern_confidence: float = 0.0
    
    def __post_init__(self):
        if self.detected_patterns is None:
            self.detected_patterns = []

@dataclass
class TradingSignal:
    """Data class untuk trading signal"""
    symbol: str
    signal_type: str  # STRONG_BUY, BUY, HOLD, SELL, STRONG_SELL
    confidence: float  # 0-100
    entry_points: List[float]
    stop_loss: float
    take_profit: List[float]
    timeframe: str
    reasoning: str
    risk_level: str  # LOW, MEDIUM, HIGH
    ai_analysis: str
    technical_score: float
    timestamp: datetime
    
class RateLimiter:
    """Rate limiter untuk API calls"""
    
    def __init__(self, max_calls: int, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def can_make_call(self) -> bool:
        """Check if we can make an API call"""
        with self.lock:
            now = time.time()
            # Remove old calls outside time window
            self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
            return len(self.calls) < self.max_calls
    
    def record_call(self):
        """Record an API call"""
        with self.lock:
            self.calls.append(time.time())
    
    def wait_if_needed(self):
        """Wait if rate limit is reached"""
        while not self.can_make_call():
            time.sleep(1)
        self.record_call()

# Helper functions for chart data
def get_binance_chart_data(symbol: str, interval: str, limit: int) -> List[Dict]:
    """Get historical candlestick data from Binance API"""
    try:
        client = BinanceAPIClient()

        # Get klines data
        klines = client.get_klines(symbol, interval, limit)
        if not klines:
            return []

        # Convert to chart format
        chart_data = []
        for kline in klines:
            chart_data.append({
                'timestamp': int(kline[0]),  # Open time
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5]),
                'close_time': int(kline[6]),
                'quote_volume': float(kline[7]),
                'trades': int(kline[8])
            })

        return chart_data

    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        return []

def get_binance_market_data(symbol: str) -> Dict:
    """Get current market data from Binance API"""
    try:
        logger.info(f"[MARKET_DATA] Fetching market data for {symbol}")
        client = BinanceAPIClient()

        # Get 24hr ticker for specific symbol
        ticker = client.get_24hr_ticker(symbol)
        if not ticker:
            logger.warning(f"[MARKET_DATA] No ticker data received for {symbol}")
            return {}

        # Validate ticker data structure
        if not isinstance(ticker, dict):
            logger.error(f"[MARKET_DATA] Invalid ticker data format for {symbol}: {type(ticker)}")
            return {}

        # Get current price
        price_data = client.get_symbol_price(symbol)
        current_price = float(price_data.get('price', 0)) if price_data else 0

        # Use ticker price as fallback if price_data is unavailable
        if current_price == 0 and ticker.get('lastPrice'):
            current_price = float(ticker.get('lastPrice', 0))

        # Validate required fields
        required_fields = ['openPrice', 'highPrice', 'lowPrice', 'volume']
        for field in required_fields:
            if field not in ticker:
                logger.warning(f"[MARKET_DATA] Missing field {field} for {symbol}")

        market_data = {
            'symbol': symbol,
            'price': current_price,
            'open': float(ticker.get('openPrice', 0)),
            'high': float(ticker.get('highPrice', 0)),
            'low': float(ticker.get('lowPrice', 0)),
            'volume': float(ticker.get('volume', 0)),
            'quote_volume': float(ticker.get('quoteVolume', 0)),
            'change': float(ticker.get('priceChange', 0)),
            'change_percent': float(ticker.get('priceChangePercent', 0)),
            'trades': int(ticker.get('count', 0)),
            'timestamp': int(time.time() * 1000)
        }

        logger.info(f"[MARKET_DATA] Successfully fetched data for {symbol}: ${current_price}")
        return market_data

    except ValueError as e:
        logger.error(f"[MARKET_DATA] Value conversion error for {symbol}: {e}")
        return {}
    except Exception as e:
        logger.error(f"[MARKET_DATA] Unexpected error getting market data for {symbol}: {e}")
        return {}

def get_binance_technical_indicators(symbol: str, interval: str) -> Dict:
    """Calculate technical indicators from chart data"""
    try:
        # Get chart data for calculations
        chart_data = get_binance_chart_data(symbol, interval, 200)  # Get more data for indicators
        if not chart_data:
            return {}

        # Extract price arrays
        closes = [float(candle['close']) for candle in chart_data]
        highs = [float(candle['high']) for candle in chart_data]
        lows = [float(candle['low']) for candle in chart_data]
        volumes = [float(candle['volume']) for candle in chart_data]

        if len(closes) < 50:  # Need enough data for calculations
            return {}

        # Calculate basic indicators (simplified for demo)
        current_price = closes[-1]

        # Simple Moving Averages
        sma_20 = sum(closes[-20:]) / 20 if len(closes) >= 20 else current_price
        sma_50 = sum(closes[-50:]) / 50 if len(closes) >= 50 else current_price
        sma_200 = sum(closes[-200:]) / 200 if len(closes) >= 200 else current_price

        # RSI calculation (simplified)
        def calculate_rsi(prices, period=14):
            if len(prices) < period + 1:
                return 50

            gains = []
            losses = []

            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period

            if avg_loss == 0:
                return 100

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi

        rsi = calculate_rsi(closes)

        # Support and resistance levels (simplified)
        recent_highs = highs[-20:]
        recent_lows = lows[-20:]
        resistance = max(recent_highs) if recent_highs else current_price
        support = min(recent_lows) if recent_lows else current_price

        # Determine trend
        trend = 'bullish' if current_price > sma_20 > sma_50 else 'bearish'

        # Generate signal
        signal = 'BUY' if rsi < 30 and trend == 'bullish' else 'SELL' if rsi > 70 and trend == 'bearish' else 'HOLD'

        return {
            'rsi': round(rsi, 2),
            'sma': {
                'sma20': round(sma_20, 6),
                'sma50': round(sma_50, 6),
                'sma200': round(sma_200, 6)
            },
            'ema': {
                'ema20': round(sma_20 * 1.02, 6),  # Simplified EMA
                'ema50': round(sma_50 * 1.01, 6),
                'ema200': round(sma_200 * 1.005, 6)
            },
            'macd': {
                'macd': round((sma_20 - sma_50), 6),
                'signal': round((sma_20 - sma_50) * 0.9, 6),
                'histogram': round((sma_20 - sma_50) * 0.1, 6)
            },
            'bollinger': {
                'upper': round(sma_20 * 1.02, 6),
                'middle': round(sma_20, 6),
                'lower': round(sma_20 * 0.98, 6)
            },
            'stochastic': {
                'k': round(min(100, max(0, (current_price - min(recent_lows)) / (max(recent_highs) - min(recent_lows)) * 100)), 2),
                'd': round(min(100, max(0, (current_price - min(recent_lows)) / (max(recent_highs) - min(recent_lows)) * 100)) * 0.9, 2)
            },
            'williams': round(-100 + (current_price - min(recent_lows)) / (max(recent_highs) - min(recent_lows)) * 100, 2),
            'atr': round((max(recent_highs) - min(recent_lows)) / len(recent_highs), 6),
            'adx': round(min(100, max(0, abs(sma_20 - sma_50) / sma_20 * 100)), 2),
            'cci': round((current_price - sma_20) / (0.015 * (max(recent_highs) - min(recent_lows))), 2),
            'momentum': round(current_price - closes[-10] if len(closes) >= 10 else 0, 6),
            'roc': round(((current_price - closes[-10]) / closes[-10] * 100) if len(closes) >= 10 and closes[-10] != 0 else 0, 2),
            'support_levels': [round(support, 6), round(support * 0.99, 6), round(support * 0.98, 6)],
            'resistance_levels': [round(resistance, 6), round(resistance * 1.01, 6), round(resistance * 1.02, 6)],
            'trend': trend,
            'signal': signal,
            'volume_avg': round(sum(volumes[-20:]) / 20 if len(volumes) >= 20 else volumes[-1], 2)
        }

    except Exception as e:
        logger.error(f"Error calculating technical indicators for {symbol}: {e}")
        return {}

class BinanceAPIClient:
    """Binance Futures API Client menggunakan requests library"""

    def __init__(self):
        self.base_url = CONFIG['binance']['base_url']
        self.timeout = CONFIG['binance']['timeout']
        self.headers = CONFIG['binance']['headers']
        self.rate_limiter = RateLimiter(CONFIG['binance']['rate_limit'], 60)
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # Configure connection pool
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=50,
            pool_maxsize=50,
            max_retries=3
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # Cache untuk data
        self.cache = {}
        self.cache_timestamps = {}

        logger.info("[BINANCE] API Client initialized")
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make HTTP request dengan rate limiting dan error handling"""
        try:
            self.rate_limiter.wait_if_needed()
            
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning(f"[BINANCE] Rate limit hit, waiting...")
                time.sleep(60)  # Wait 1 minute
                return self._make_request(endpoint, params)
            else:
                logger.error(f"[BINANCE] API error {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error(f"[BINANCE] Request timeout for {endpoint}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"[BINANCE] Request error for {endpoint}: {e}")
            return None
        except Exception as e:
            logger.error(f"[BINANCE] Unexpected error for {endpoint}: {e}")
            return None

    def _get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached data if still valid"""
        if key in self.cache and key in self.cache_timestamps:
            if time.time() - self.cache_timestamps[key] < CONFIG['performance']['cache_expiry']:
                return self.cache[key]
        return None

    def _set_cached_data(self, key: str, data: Any):
        """Set data in cache"""
        self.cache[key] = data
        self.cache_timestamps[key] = time.time()

    def get_exchange_info(self) -> Optional[Dict]:
        """Get exchange information untuk futures"""
        cache_key = "exchange_info"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        data = self._make_request("/fapi/v1/exchangeInfo")
        if data:
            self._set_cached_data(cache_key, data)
        return data

    def get_24hr_ticker(self, symbol: str = None) -> Optional[Dict]:
        """Get 24hr ticker statistics for specific symbol or all symbols"""
        try:
            if symbol:
                # Get ticker for specific symbol
                cache_key = f"24hr_ticker_{symbol}"
                cached_data = self._get_cached_data(cache_key)
                if cached_data:
                    return cached_data

                # Request specific symbol ticker
                params = {"symbol": symbol}
                data = self._make_request("/fapi/v1/ticker/24hr", params=params)
                if data:
                    self._set_cached_data(cache_key, data)
                return data
            else:
                # Get all symbols ticker (legacy behavior)
                cache_key = "24hr_ticker_all"
                cached_data = self._get_cached_data(cache_key)
                if cached_data:
                    return cached_data

                data = self._make_request("/fapi/v1/ticker/24hr")
                if data:
                    self._set_cached_data(cache_key, data)
                return data

        except Exception as e:
            logger.error(f"Error getting 24hr ticker for {symbol}: {e}")
            return None

    def get_symbol_price(self, symbol: str) -> Optional[Dict]:
        """Get current price for specific symbol"""
        try:
            cache_key = f"symbol_price_{symbol}"
            cached_data = self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            # Request current price
            params = {"symbol": symbol}
            data = self._make_request("/fapi/v1/ticker/price", params=params)
            if data:
                self._set_cached_data(cache_key, data)
            return data

        except Exception as e:
            logger.error(f"Error getting symbol price for {symbol}: {e}")
            return None

    def get_klines(self, symbol: str, interval: str = "1h", limit: int = 250) -> Optional[List[List]]:
        """Get candlestick data untuk symbol"""
        cache_key = f"klines_{symbol}_{interval}_{limit}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        data = self._make_request("/fapi/v1/klines", params)
        if data:
            self._set_cached_data(cache_key, data)
        return data

    def get_top_pairs(self, limit: int = 500) -> List[TradingPair]:
        """Get top trading pairs berdasarkan volume from full Binance futures universe"""
        try:
            # Get all available futures pairs from exchange info
            exchange_info = self.get_exchange_info()
            if not exchange_info or 'symbols' not in exchange_info:
                logger.warning("[BINANCE] Exchange info not available, falling back to ticker data")
                return self._get_pairs_from_ticker(limit)

            # Get active USDT futures pairs
            active_symbols = set()
            for symbol_info in exchange_info['symbols']:
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('quoteAsset') == 'USDT' and
                    symbol_info.get('contractType') == 'PERPETUAL'):
                    active_symbols.add(symbol_info['symbol'])

            logger.info(f"[BINANCE] Found {len(active_symbols)} active USDT perpetual futures")

            # Get ticker data for volume filtering
            ticker_data = self.get_24hr_ticker()
            if not ticker_data:
                logger.error("[BINANCE] No ticker data available")
                return []

            # Create ticker lookup for faster access
            ticker_lookup = {ticker['symbol']: ticker for ticker in ticker_data}

            # Filter and create trading pairs
            valid_pairs = []
            for symbol in active_symbols:
                if symbol in ticker_lookup:
                    ticker = ticker_lookup[symbol]
                    try:
                        # Apply volume filter: > $300K for maximum coverage of 500+ pairs
                        volume_24h = float(ticker['quoteVolume'])
                        if volume_24h > 300000:  # Reduced to $300K for broader coverage
                            pair = TradingPair(
                                symbol=ticker['symbol'],
                                base_asset=ticker['symbol'][:-4],  # Remove USDT
                                quote_asset='USDT',
                                price=float(ticker['lastPrice']),
                                volume_24h=volume_24h,
                                change_24h=float(ticker['priceChangePercent']),
                                high_24h=float(ticker['highPrice']),
                                low_24h=float(ticker['lowPrice']),
                                last_update=datetime.now()
                            )
                            valid_pairs.append(pair)
                    except (ValueError, KeyError) as e:
                        continue

            # Sort berdasarkan volume dan ambil top pairs
            valid_pairs.sort(key=lambda x: x.volume_24h, reverse=True)

            logger.info(f"[BINANCE] Filtered to {len(valid_pairs)} pairs with >$300K volume, returning top {min(limit, len(valid_pairs))}")

            return valid_pairs[:limit]

        except Exception as e:
            logger.error(f"[BINANCE] Error getting top pairs: {e}")
            # Fallback to ticker-only method
            return self._get_pairs_from_ticker(limit)

    def _get_pairs_from_ticker(self, limit: int) -> List[TradingPair]:
        """Fallback method using only ticker data"""
        try:
            ticker_data = self.get_24hr_ticker()
            if not ticker_data:
                return []

            valid_pairs = []
            for ticker in ticker_data:
                try:
                    # Filter hanya USDT pairs dan volume > $500K
                    if (ticker['symbol'].endswith('USDT') and
                        float(ticker['quoteVolume']) > 500000):

                        pair = TradingPair(
                            symbol=ticker['symbol'],
                            base_asset=ticker['symbol'][:-4],
                            quote_asset='USDT',
                            price=float(ticker['lastPrice']),
                            volume_24h=float(ticker['quoteVolume']),
                            change_24h=float(ticker['priceChangePercent']),
                            high_24h=float(ticker['highPrice']),
                            low_24h=float(ticker['lowPrice']),
                            last_update=datetime.now()
                        )
                        valid_pairs.append(pair)
                except (ValueError, KeyError) as e:
                    continue

            valid_pairs.sort(key=lambda x: x.volume_24h, reverse=True)
            return valid_pairs[:limit]

        except Exception as e:
            logger.error(f"[BINANCE] Error in fallback method: {e}")
            return []

    def get_candlestick_data(self, symbol: str, interval: str = "1h", limit: int = 250) -> List[CandlestickData]:
        """Get candlestick data dalam format yang mudah digunakan"""
        try:
            klines = self.get_klines(symbol, interval, limit)
            if not klines:
                return []

            candlesticks = []
            for kline in klines:
                candlestick = CandlestickData(
                    open_time=datetime.fromtimestamp(kline[0] / 1000),
                    open_price=float(kline[1]),
                    high_price=float(kline[2]),
                    low_price=float(kline[3]),
                    close_price=float(kline[4]),
                    volume=float(kline[5]),
                    close_time=datetime.fromtimestamp(kline[6] / 1000),
                    quote_volume=float(kline[7]),
                    trades_count=int(kline[8])
                )
                candlesticks.append(candlestick)

            return candlesticks

        except Exception as e:
            logger.error(f"[BINANCE] Error getting candlestick data for {symbol}: {e}")
            return []

    def health_check(self) -> bool:
        """Check if Binance API is healthy"""
        try:
            response = self._make_request("/fapi/v1/ping")
            return response is not None
        except:
            return False

class TechnicalAnalysisEngine:
    """Engine untuk menghitung 60+ technical indicators menggunakan ta library"""

    def __init__(self):
        logger.info("[TA] Technical Analysis Engine initialized")

    def calculate_indicators(self, candlesticks: List[CandlestickData]) -> TechnicalIndicators:
        """Calculate semua technical indicators"""
        try:
            if len(candlesticks) < 200:  # Need enough data for calculations
                logger.warning(f"[TA] Insufficient data: {len(candlesticks)} candlesticks")
                return TechnicalIndicators()

            # Convert to pandas DataFrame
            df = pd.DataFrame([{
                'open': c.open_price,
                'high': c.high_price,
                'low': c.low_price,
                'close': c.close_price,
                'volume': c.volume
            } for c in candlesticks])

            indicators = TechnicalIndicators()

            # Trend Indicators
            indicators.sma_9 = SMAIndicator(df['close'], window=9).sma_indicator().iloc[-1]
            indicators.sma_21 = SMAIndicator(df['close'], window=21).sma_indicator().iloc[-1]
            indicators.sma_50 = SMAIndicator(df['close'], window=50).sma_indicator().iloc[-1]
            indicators.sma_200 = SMAIndicator(df['close'], window=200).sma_indicator().iloc[-1]

            indicators.ema_9 = EMAIndicator(df['close'], window=9).ema_indicator().iloc[-1]
            indicators.ema_21 = EMAIndicator(df['close'], window=21).ema_indicator().iloc[-1]
            indicators.ema_50 = EMAIndicator(df['close'], window=50).ema_indicator().iloc[-1]
            indicators.ema_200 = EMAIndicator(df['close'], window=200).ema_indicator().iloc[-1]

            macd = MACD(df['close'])
            indicators.macd_line = macd.macd().iloc[-1]
            indicators.macd_signal = macd.macd_signal().iloc[-1]
            indicators.macd_histogram = macd.macd_diff().iloc[-1]

            indicators.adx = ADXIndicator(df['high'], df['low'], df['close']).adx().iloc[-1]
            indicators.psar = PSARIndicator(df['high'], df['low'], df['close']).psar().iloc[-1]

            # Momentum Indicators
            indicators.rsi = RSIIndicator(df['close']).rsi().iloc[-1]

            stoch = StochasticOscillator(df['high'], df['low'], df['close'])
            indicators.stoch_k = stoch.stoch().iloc[-1]
            indicators.stoch_d = stoch.stoch_signal().iloc[-1]

            indicators.williams_r = WilliamsRIndicator(df['high'], df['low'], df['close']).williams_r().iloc[-1]

            # CCI calculation using manual formula (ta.momentum doesn't have cci in this version)
            try:
                # Commodity Channel Index calculation
                typical_price = (df['high'] + df['low'] + df['close']) / 3
                sma_tp = typical_price.rolling(window=20).mean()
                mad = typical_price.rolling(window=20).apply(lambda x: abs(x - x.mean()).mean())
                if not mad.isna().iloc[-1] and mad.iloc[-1] != 0:
                    indicators.cci = (typical_price.iloc[-1] - sma_tp.iloc[-1]) / (0.015 * mad.iloc[-1])
                else:
                    indicators.cci = 0.0
            except Exception as e:
                logger.debug(f"[TA] CCI calculation error: {e}")
                indicators.cci = 0.0
            indicators.roc = ROCIndicator(df['close']).roc().iloc[-1]
            indicators.stoch_rsi = StochRSIIndicator(df['close']).stochrsi().iloc[-1]

            # Volatility Indicators
            bb = BollingerBands(df['close'])
            indicators.bb_upper = bb.bollinger_hband().iloc[-1]
            indicators.bb_middle = bb.bollinger_mavg().iloc[-1]
            indicators.bb_lower = bb.bollinger_lband().iloc[-1]

            indicators.atr = AverageTrueRange(df['high'], df['low'], df['close']).average_true_range().iloc[-1]

            keltner = KeltnerChannel(df['high'], df['low'], df['close'])
            indicators.keltner_upper = keltner.keltner_channel_hband().iloc[-1]
            indicators.keltner_middle = keltner.keltner_channel_mband().iloc[-1]
            indicators.keltner_lower = keltner.keltner_channel_lband().iloc[-1]

            # Volume Indicators
            indicators.obv = OnBalanceVolumeIndicator(df['close'], df['volume']).on_balance_volume().iloc[-1]
            indicators.vpt = VolumePriceTrendIndicator(df['close'], df['volume']).volume_price_trend().iloc[-1]
            indicators.adi = AccDistIndexIndicator(df['high'], df['low'], df['close'], df['volume']).acc_dist_index().iloc[-1]
            indicators.cmf = ChaikinMoneyFlowIndicator(df['high'], df['low'], df['close'], df['volume']).chaikin_money_flow().iloc[-1]

            # Support/Resistance Levels
            indicators.support_level, indicators.resistance_level = self._calculate_support_resistance(df)
            indicators.pivot_point = self._calculate_pivot_point(candlesticks[-1])

            # Pattern Recognition
            indicators.detected_patterns, indicators.pattern_confidence = self._detect_patterns(df)

            return indicators

        except Exception as e:
            logger.error(f"[TA] Error calculating indicators: {e}")
            return TechnicalIndicators()

    def _calculate_support_resistance(self, df: pd.DataFrame) -> Tuple[float, float]:
        """Calculate support and resistance levels"""
        try:
            # Use last 50 candles for calculation
            recent_data = df.tail(50)

            # Find local minima and maxima
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Simple support/resistance calculation
            resistance = np.percentile(highs, 95)
            support = np.percentile(lows, 5)

            return support, resistance

        except Exception as e:
            logger.error(f"[TA] Error calculating support/resistance: {e}")
            return 0.0, 0.0

    def _calculate_pivot_point(self, last_candle: CandlestickData) -> float:
        """Calculate pivot point"""
        try:
            return (last_candle.high_price + last_candle.low_price + last_candle.close_price) / 3
        except:
            return 0.0

    def _detect_patterns(self, df: pd.DataFrame) -> Tuple[List[str], float]:
        """Detect chart patterns"""
        try:
            patterns = []
            confidence = 0.0

            # Simple pattern detection
            recent_closes = df['close'].tail(20).values

            # Trend detection
            if len(recent_closes) >= 10:
                trend_slope = np.polyfit(range(len(recent_closes)), recent_closes, 1)[0]

                if trend_slope > 0:
                    patterns.append("Uptrend")
                    confidence += 20
                elif trend_slope < 0:
                    patterns.append("Downtrend")
                    confidence += 20
                else:
                    patterns.append("Sideways")
                    confidence += 10

            # Bollinger Band squeeze detection
            bb = BollingerBands(df['close'])
            bb_width = (bb.bollinger_hband() - bb.bollinger_lband()) / bb.bollinger_mavg()

            if bb_width.iloc[-1] < bb_width.rolling(20).mean().iloc[-1] * 0.8:
                patterns.append("Bollinger Squeeze")
                confidence += 15

            # Volume spike detection
            volume_ma = df['volume'].rolling(20).mean()
            if df['volume'].iloc[-1] > volume_ma.iloc[-1] * 1.5:
                patterns.append("Volume Spike")
                confidence += 10

            return patterns, min(confidence, 100.0)

        except Exception as e:
            logger.error(f"[TA] Error detecting patterns: {e}")
            return [], 0.0

    def calculate_signal_strength(self, indicators: TechnicalIndicators, current_price: float) -> float:
        """Calculate overall signal strength (0-100)"""
        try:
            score = 0.0
            max_score = 100.0

            # Trend Analysis (30 points)
            trend_score = 0
            if current_price > indicators.sma_50 > indicators.sma_200:
                trend_score += 15  # Strong uptrend
            elif current_price > indicators.sma_21:
                trend_score += 10  # Moderate uptrend
            elif current_price < indicators.sma_50 < indicators.sma_200:
                trend_score -= 15  # Strong downtrend
            elif current_price < indicators.sma_21:
                trend_score -= 10  # Moderate downtrend

            if indicators.macd_line > indicators.macd_signal:
                trend_score += 10
            else:
                trend_score -= 10

            if indicators.adx > 25:
                trend_score += 5  # Strong trend

            score += max(0, min(30, trend_score + 15))  # Normalize to 0-30

            # Momentum Analysis (25 points)
            momentum_score = 0

            # RSI analysis
            if 30 <= indicators.rsi <= 70:
                momentum_score += 10  # Neutral zone
            elif indicators.rsi < 30:
                momentum_score += 15  # Oversold (bullish)
            elif indicators.rsi > 70:
                momentum_score -= 15  # Overbought (bearish)

            # Stochastic analysis
            if indicators.stoch_k > indicators.stoch_d:
                momentum_score += 5
            else:
                momentum_score -= 5

            # Williams %R
            if indicators.williams_r < -80:
                momentum_score += 5  # Oversold
            elif indicators.williams_r > -20:
                momentum_score -= 5  # Overbought

            score += max(0, min(25, momentum_score + 12.5))  # Normalize to 0-25

            # Support/Resistance Analysis (20 points)
            sr_score = 0
            price_position = (current_price - indicators.support_level) / (indicators.resistance_level - indicators.support_level)

            if 0.3 <= price_position <= 0.7:
                sr_score += 15  # Good position
            elif price_position < 0.2:
                sr_score += 10  # Near support (potential bounce)
            elif price_position > 0.8:
                sr_score -= 10  # Near resistance (potential rejection)

            score += max(0, min(20, sr_score + 10))  # Normalize to 0-20

            # Volume Analysis (15 points)
            volume_score = 10  # Base score
            if indicators.cmf > 0:
                volume_score += 5  # Positive money flow

            score += volume_score

            # Pattern Recognition (10 points)
            pattern_score = indicators.pattern_confidence / 10  # Convert to 0-10 scale
            score += pattern_score

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.error(f"[TA] Error calculating signal strength: {e}")
            return 50.0  # Neutral score on error

class AISignalGenerator:
    """AI-powered signal generator menggunakan Google Gemini"""

    def __init__(self):
        self.api_key = CONFIG['ai']['api_key']
        self.model_name = CONFIG['ai']['model']
        self.rate_limiter = RateLimiter(CONFIG['ai']['rate_limit'], 60)

        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logger.info("[AI] Gemini AI initialized")
        else:
            self.model = None
            logger.warning("[AI] No Gemini API key provided, AI features disabled")

    def generate_signal(self, pair: TradingPair, indicators: TechnicalIndicators,
                       candlesticks: List[CandlestickData]) -> TradingSignal:
        """Generate comprehensive trading signal"""
        try:
            # Calculate technical score
            technical_score = self._calculate_technical_score(indicators, pair.price)

            # Generate AI analysis if available
            ai_analysis = ""
            if self.model and self.api_key:
                ai_analysis = self._generate_ai_analysis(pair, indicators, candlesticks)

            # Determine signal type based on technical score
            signal_type = self._determine_signal_type(technical_score)

            # Calculate entry points, stop loss, take profit
            entry_points = self._calculate_entry_points(pair.price, indicators)
            stop_loss = self._calculate_stop_loss(pair.price, indicators, signal_type)
            take_profit = self._calculate_take_profit(pair.price, indicators, signal_type)

            # Determine risk level
            risk_level = self._calculate_risk_level(indicators, pair.change_24h)

            # Generate reasoning
            reasoning = self._generate_reasoning(indicators, technical_score)

            signal = TradingSignal(
                symbol=pair.symbol,
                signal_type=signal_type,
                confidence=technical_score,
                entry_points=entry_points,
                stop_loss=stop_loss,
                take_profit=take_profit,
                timeframe="1H",
                reasoning=reasoning,
                risk_level=risk_level,
                ai_analysis=ai_analysis,
                technical_score=technical_score,
                timestamp=datetime.now()
            )

            return signal

        except Exception as e:
            logger.error(f"[AI] Error generating signal for {pair.symbol}: {e}")
            return self._create_default_signal(pair)

    def _calculate_technical_score(self, indicators: TechnicalIndicators, current_price: float) -> float:
        """Calculate technical analysis score"""
        try:
            score = 0.0

            # Trend signals (40% weight)
            trend_signals = 0
            if current_price > indicators.ema_21:
                trend_signals += 1
            if indicators.ema_21 > indicators.ema_50:
                trend_signals += 1
            if indicators.ema_50 > indicators.ema_200:
                trend_signals += 1
            if indicators.macd_line > indicators.macd_signal:
                trend_signals += 1
            if indicators.adx > 25:
                trend_signals += 1

            score += (trend_signals / 5) * 40

            # Momentum signals (30% weight)
            momentum_score = 0
            if 30 <= indicators.rsi <= 70:
                momentum_score += 20
            elif indicators.rsi < 30:
                momentum_score += 30  # Oversold
            elif indicators.rsi > 70:
                momentum_score -= 10  # Overbought

            if indicators.stoch_k > indicators.stoch_d:
                momentum_score += 10

            score += max(0, min(30, momentum_score))

            # Volume confirmation (20% weight)
            if indicators.cmf > 0:
                score += 15
            if indicators.obv > 0:
                score += 5

            # Pattern recognition (10% weight)
            score += indicators.pattern_confidence / 10

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.error(f"[AI] Error calculating technical score: {e}")
            return 50.0

    def _generate_ai_analysis(self, pair: TradingPair, indicators: TechnicalIndicators,
                             candlesticks: List[CandlestickData]) -> str:
        """Generate AI analysis menggunakan Gemini"""
        try:
            if not self.model:
                return "AI analysis tidak tersedia (API key tidak dikonfigurasi)"

            self.rate_limiter.wait_if_needed()

            # Create comprehensive prompt
            prompt = self._create_analysis_prompt(pair, indicators, candlesticks)

            response = self.model.generate_content(prompt)

            if response and response.text:
                return response.text.strip()
            else:
                return "AI analysis gagal dihasilkan"

        except Exception as e:
            logger.error(f"[AI] Error generating AI analysis: {e}")
            return f"AI analysis error: {str(e)}"

    def _create_analysis_prompt(self, pair: TradingPair, indicators: TechnicalIndicators,
                               candlesticks: List[CandlestickData]) -> str:
        """Create detailed analysis prompt untuk Gemini"""

        # Get recent price action
        recent_candles = candlesticks[-10:] if len(candlesticks) >= 10 else candlesticks
        price_action = ", ".join([f"${c.close_price:.4f}" for c in recent_candles])

        prompt = f"""🚀 ANALISIS TRADING PROFESIONAL - {pair.symbol} 🚀

Anda adalah seorang trader cryptocurrency profesional dengan pengalaman 10+ tahun di pasar futures Binance. Analisis pair berikut dengan detail dan berikan rekomendasi trading yang akurat.

📊 DATA PASAR REAL-TIME:
• PAIR: {pair.symbol}
• HARGA SAAT INI: ${pair.price:.4f}
• PERUBAHAN 24H: {pair.change_24h:.2f}%
• VOLUME 24H: ${pair.volume_24h:,.0f} USDT
• HIGH 24H: ${pair.high_24h:.4f} | LOW 24H: ${pair.low_24h:.4f}
• TIMEFRAME ANALISIS: 1H, 30M, 15M (Multi-timeframe)
• AKSI HARGA TERAKHIR: {price_action}

📈 ANALISIS TEKNIKAL KOMPREHENSIF:

🔄 INDIKATOR TREND:
• SMA(21): ${indicators.sma_21:.4f} | SMA(50): ${indicators.sma_50:.4f} | SMA(200): ${indicators.sma_200:.4f}
• EMA(21): ${indicators.ema_21:.4f} | EMA(50): ${indicators.ema_50:.4f}
• MACD: Line {indicators.macd_line:.4f} | Signal {indicators.macd_signal:.4f} | Histogram {indicators.macd_histogram:.4f}
• ADX: {indicators.adx:.2f} (Kekuatan Trend) | PSAR: ${indicators.psar:.4f}

⚡ INDIKATOR MOMENTUM:
• RSI(14): {indicators.rsi:.2f} | Stochastic K: {indicators.stoch_k:.2f} | D: {indicators.stoch_d:.2f}
• Williams %R: {indicators.williams_r:.2f} | CCI(20): {indicators.cci:.2f}
• ROC: {indicators.roc:.2f}% | Stochastic RSI: {indicators.stoch_rsi:.2f}

🌊 VOLATILITAS & SUPPORT/RESISTANCE:
• Bollinger Bands: Upper ${indicators.bb_upper:.4f} | Middle ${indicators.bb_middle:.4f} | Lower ${indicators.bb_lower:.4f}
• ATR: {indicators.atr:.4f} (Volatilitas) | Pivot Point: ${indicators.pivot_point:.4f}
• SUPPORT KUNCI: ${indicators.support_level:.4f} | RESISTANCE KUNCI: ${indicators.resistance_level:.4f}

📊 ANALISIS VOLUME:
• OBV: {indicators.obv:,.0f} | Chaikin Money Flow: {indicators.cmf:.4f}
• Volume Price Trend: {indicators.vpt:,.0f} | Accumulation/Distribution: {indicators.adi:,.0f}

🎯 PATTERN RECOGNITION:
• Pattern Terdeteksi: {', '.join(indicators.detected_patterns) if indicators.detected_patterns else 'Tidak ada pattern signifikan'}
• Confidence Level Pattern: {indicators.pattern_confidence:.1f}%

🔍 INSTRUKSI ANALISIS MENDALAM:

1. LAKUKAN RISET INTERNET TAMBAHAN tentang:
   - Berita fundamental terkait {pair.symbol} dalam 24 jam terakhir
   - Sentiment pasar crypto secara umum
   - Update development atau partnership terbaru
   - Analisis on-chain metrics jika tersedia

2. BERIKAN KEPUTUSAN TRADING EKSPLISIT:
   - STRONG BUY (80-100% confidence)
   - BUY (60-79% confidence)
   - HOLD (40-59% confidence)
   - SELL (20-39% confidence)
   - STRONG SELL (0-19% confidence)

3. TENTUKAN STRATEGI ENTRY BERTINGKAT:
   - Entry Point 1 (Aggressive): $X.XXXX
   - Entry Point 2 (Conservative): $X.XXXX
   - Entry Point 3 (Deep Value): $X.XXXX
   - Berikan reasoning untuk setiap level

4. MANAJEMEN RISIKO UNTUK MODAL 3 JUTA RUPIAH:
   - Stop Loss: $X.XXXX (X% dari entry)
   - Take Profit 1: $X.XXXX (Risk:Reward 1:2)
   - Take Profit 2: $X.XXXX (Risk:Reward 1:3)
   - Take Profit 3: $X.XXXX (Risk:Reward 1:5)
   - Leverage yang disarankan: Xx (dengan reasoning)
   - Position size yang aman: $XXX USDT

5. TIMEFRAME & TARGET:
   - Jenis trading: Scalping/Swing/Position
   - Estimasi holding period: X jam/hari
   - Target profit realistis: X%
   - Maximum drawdown yang dapat diterima: X%

6. RISK ASSESSMENT:
   - Market condition saat ini (Bull/Bear/Sideways)
   - Faktor risiko utama yang perlu diwaspadai
   - Kondisi yang membatalkan analisis ini
   - Saran untuk risk management

FORMAT RESPON: Gunakan emoji, struktur yang jelas seperti sinyal Telegram profesional, bahasa Indonesia yang mudah dipahami, dan sertakan confidence percentage untuk setiap rekomendasi.

WAJIB DIAKHIRI DENGAN: "🤖 Signal Analyzer AI by BOBACHEESE | Powered by Gemini 2.0 Flash" """

        return prompt

    def _determine_signal_type(self, technical_score: float) -> str:
        """Determine signal type berdasarkan technical score"""
        if technical_score >= 80:
            return "STRONG_BUY"
        elif technical_score >= 65:
            return "BUY"
        elif technical_score >= 35:
            return "HOLD"
        elif technical_score >= 20:
            return "SELL"
        else:
            return "STRONG_SELL"

    def _calculate_entry_points(self, current_price: float, indicators: TechnicalIndicators) -> List[float]:
        """Calculate entry points"""
        try:
            entry_points = []

            # Primary entry at current price
            entry_points.append(current_price)

            # Secondary entry at support level
            if indicators.support_level > 0:
                entry_points.append(indicators.support_level * 1.01)  # Slightly above support

            # Third entry at moving average
            if indicators.ema_21 > 0:
                entry_points.append(indicators.ema_21)

            return sorted(entry_points)[:3]  # Maximum 3 entry points

        except Exception as e:
            logger.error(f"[AI] Error calculating entry points: {e}")
            return [current_price]

    def _calculate_stop_loss(self, current_price: float, indicators: TechnicalIndicators, signal_type: str) -> float:
        """Calculate stop loss level"""
        try:
            if signal_type in ["STRONG_BUY", "BUY"]:
                # For buy signals, stop loss below support
                if indicators.support_level > 0:
                    return indicators.support_level * 0.98
                else:
                    return current_price * 0.95  # 5% stop loss
            else:
                # For sell signals, stop loss above resistance
                if indicators.resistance_level > 0:
                    return indicators.resistance_level * 1.02
                else:
                    return current_price * 1.05  # 5% stop loss

        except Exception as e:
            logger.error(f"[AI] Error calculating stop loss: {e}")
            return current_price * 0.95

    def _calculate_take_profit(self, current_price: float, indicators: TechnicalIndicators, signal_type: str) -> List[float]:
        """Calculate take profit levels"""
        try:
            take_profits = []

            if signal_type in ["STRONG_BUY", "BUY"]:
                # For buy signals
                take_profits.append(current_price * 1.03)  # 3% profit
                take_profits.append(current_price * 1.06)  # 6% profit
                if indicators.resistance_level > current_price:
                    take_profits.append(indicators.resistance_level * 0.98)  # Near resistance
                else:
                    take_profits.append(current_price * 1.10)  # 10% profit
            else:
                # For sell signals
                take_profits.append(current_price * 0.97)  # 3% profit
                take_profits.append(current_price * 0.94)  # 6% profit
                if indicators.support_level < current_price:
                    take_profits.append(indicators.support_level * 1.02)  # Near support
                else:
                    take_profits.append(current_price * 0.90)  # 10% profit

            return sorted(take_profits, reverse=(signal_type in ["STRONG_BUY", "BUY"]))[:3]

        except Exception as e:
            logger.error(f"[AI] Error calculating take profit: {e}")
            return [current_price * 1.05]

    def _calculate_risk_level(self, indicators: TechnicalIndicators, change_24h: float) -> str:
        """Calculate risk level"""
        try:
            risk_score = 0

            # Volatility risk
            if abs(change_24h) > 10:
                risk_score += 2
            elif abs(change_24h) > 5:
                risk_score += 1

            # ATR risk
            if indicators.atr > 0:
                # High ATR indicates high volatility
                if indicators.atr > indicators.bb_middle * 0.05:
                    risk_score += 2
                elif indicators.atr > indicators.bb_middle * 0.03:
                    risk_score += 1

            # RSI extreme levels
            if indicators.rsi > 80 or indicators.rsi < 20:
                risk_score += 1

            if risk_score >= 4:
                return "HIGH"
            elif risk_score >= 2:
                return "MEDIUM"
            else:
                return "LOW"

        except Exception as e:
            logger.error(f"[AI] Error calculating risk level: {e}")
            return "MEDIUM"

    def _generate_reasoning(self, indicators: TechnicalIndicators, technical_score: float) -> str:
        """Generate reasoning untuk signal"""
        try:
            reasons = []

            # Trend analysis
            if indicators.ema_21 > indicators.ema_50:
                reasons.append("EMA menunjukkan uptrend")
            elif indicators.ema_21 < indicators.ema_50:
                reasons.append("EMA menunjukkan downtrend")

            # MACD analysis
            if indicators.macd_line > indicators.macd_signal:
                reasons.append("MACD bullish crossover")
            else:
                reasons.append("MACD bearish crossover")

            # RSI analysis
            if indicators.rsi < 30:
                reasons.append("RSI oversold (potensi bounce)")
            elif indicators.rsi > 70:
                reasons.append("RSI overbought (potensi koreksi)")
            else:
                reasons.append("RSI dalam zona netral")

            # Volume analysis
            if indicators.cmf > 0:
                reasons.append("Money flow positif")
            else:
                reasons.append("Money flow negatif")

            # Pattern analysis
            if indicators.detected_patterns:
                reasons.append(f"Pattern terdeteksi: {', '.join(indicators.detected_patterns)}")

            return " | ".join(reasons)

        except Exception as e:
            logger.error(f"[AI] Error generating reasoning: {e}")
            return "Technical analysis menunjukkan kondisi mixed signals"

    def _create_default_signal(self, pair: TradingPair) -> TradingSignal:
        """Create default signal jika terjadi error"""
        return TradingSignal(
            symbol=pair.symbol,
            signal_type="HOLD",
            confidence=50.0,
            entry_points=[pair.price],
            stop_loss=pair.price * 0.95,
            take_profit=[pair.price * 1.05],
            timeframe="1H",
            reasoning="Default signal - insufficient data",
            risk_level="MEDIUM",
            ai_analysis="AI analysis tidak tersedia",
            technical_score=50.0,
            timestamp=datetime.now()
        )

class SignalEngine:
    """Main engine untuk signal generation"""

    def __init__(self):
        self.binance_client = BinanceAPIClient()
        self.ta_engine = TechnicalAnalysisEngine()
        self.ai_generator = AISignalGenerator()

        # Performance tracking
        self.stats = {
            'total_analyzed': 0,
            'signals_generated': 0,
            'analysis_time': 0,
            'last_update': None
        }

        logger.info("[ENGINE] Signal Engine initialized")

    def analyze_pair(self, pair: TradingPair) -> Optional[TradingSignal]:
        """Analyze single trading pair"""
        try:
            start_time = time.time()

            # Get candlestick data
            candlesticks = self.binance_client.get_candlestick_data(pair.symbol)
            if not candlesticks or len(candlesticks) < 200:
                logger.warning(f"[ENGINE] Insufficient data for {pair.symbol}")
                return None

            # Calculate technical indicators
            indicators = self.ta_engine.calculate_indicators(candlesticks)

            # Generate signal
            signal = self.ai_generator.generate_signal(pair, indicators, candlesticks)

            # Update stats
            analysis_time = time.time() - start_time
            self.stats['total_analyzed'] += 1
            self.stats['signals_generated'] += 1
            self.stats['analysis_time'] += analysis_time
            self.stats['last_update'] = datetime.now()

            logger.info(f"[ENGINE] Analyzed {pair.symbol} in {analysis_time:.2f}s - Signal: {signal.signal_type}")

            return signal

        except Exception as e:
            logger.error(f"[ENGINE] Error analyzing {pair.symbol}: {e}")
            return None

    def analyze_multiple_pairs(self, pairs: List[TradingPair], max_workers: int = 20) -> List[TradingSignal]:
        """Analyze multiple pairs dengan parallel processing"""
        try:
            signals = []

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_pair = {executor.submit(self.analyze_pair, pair): pair for pair in pairs}

                # Collect results
                for future in as_completed(future_to_pair):
                    pair = future_to_pair[future]
                    try:
                        signal = future.result(timeout=30)  # 30 second timeout per pair
                        if signal:
                            signals.append(signal)
                    except Exception as e:
                        logger.error(f"[ENGINE] Error processing {pair.symbol}: {e}")

            # Sort by confidence
            signals.sort(key=lambda x: x.confidence, reverse=True)

            logger.info(f"[ENGINE] Generated {len(signals)} signals from {len(pairs)} pairs")

            return signals

        except Exception as e:
            logger.error(f"[ENGINE] Error in batch analysis: {e}")
            return []

    def get_top_signals(self, limit: int = None) -> List[TradingSignal]:
        """Get top trading signals from all available pairs"""
        try:
            # Get all available pairs (up to target_pairs limit)
            max_pairs = CONFIG['performance']['target_pairs']
            pairs = self.binance_client.get_top_pairs(max_pairs)
            if not pairs:
                logger.warning("[ENGINE] No pairs available")
                return []

            logger.info(f"[ENGINE] Analyzing {len(pairs)} pairs for signal generation")

            # Analyze all pairs (remove artificial limit)
            # Use limit only for final result filtering, not input filtering
            signals = self.analyze_multiple_pairs(pairs, CONFIG['performance']['max_workers'])

            # Apply limit only to final results if specified
            if limit and limit > 0:
                signals = signals[:limit]
                logger.info(f"[ENGINE] Returning top {len(signals)} signals (limited from {len(pairs)} pairs)")
            else:
                logger.info(f"[ENGINE] Returning all {len(signals)} signals from {len(pairs)} pairs")

            return signals

        except Exception as e:
            logger.error(f"[ENGINE] Error getting top signals: {e}")
            return []

# Flask Application
app = Flask(__name__)
app.config['SECRET_KEY'] = 'binance-signal-generator-pro-2024'

# Initialize CORS with fallback
if CORS_AVAILABLE:
    CORS(app)
else:
    # Manual CORS headers
    @app.after_request
    def after_request(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

# Global signal engine
signal_engine = SignalEngine()

# Global analysis control variables
analysis_status = {
    'is_running': False,
    'progress': 0,
    'total_pairs': 0,
    'current_pair': '',
    'start_time': None,
    'estimated_completion': None,
    'last_update': None,
    'error_message': None
}
analysis_lock = threading.Lock()

@app.route('/')
def index():
    """Halaman utama - New UI"""
    from datetime import datetime
    from flask import make_response

    response = make_response(render_template('index.html', current_date=datetime.now().strftime('%Y-%m-%d')))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/old')
def old_index():
    """Old UI page (backup)"""
    return render_template('index.html')

@app.route('/new')
def new_index():
    """New redesigned UI page (alias)"""
    try:
        from datetime import datetime
        return render_template('new-index.html', current_date=datetime.now().strftime('%Y-%m-%d'))
    except Exception as e:
        return f"Error loading new UI: {str(e)}", 500

@app.route('/test-new')
def test_new():
    """Test route for new UI"""
    return "New UI route is working!"

@app.route('/ui-new')
def ui_new():
    """Force new UI without cache"""
    from datetime import datetime
    from flask import make_response

    response = make_response(render_template('index.html', current_date=datetime.now().strftime('%Y-%m-%d')))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    response.headers['X-UI-Version'] = 'new-2.0'
    return response

@app.route('/api/signals')
def get_signals():
    """API endpoint untuk mendapatkan trading signals with advanced filtering and sorting"""
    try:
        # Validate and process parameters
        try:
            limit = request.args.get('limit', 10, type=int)
            if limit < 5 or limit > 50:
                return jsonify({
                    "success": False,
                    "error": "Limit must be between 5 and 50"
                }), 400
        except (ValueError, TypeError):
            return jsonify({
                "success": False,
                "error": "Invalid limit parameter"
            }), 400

        # Advanced filtering parameters
        signal_type_filter = request.args.get('signal_type', '').upper()  # STRONG_BUY, BUY, etc.
        min_confidence = request.args.get('min_confidence', 0, type=float)
        min_volume = request.args.get('min_volume', 0, type=float)
        sort_by = request.args.get('sort_by', 'confidence')  # confidence, volume, change_24h

        # Get all signals from expanded market coverage (500+ pairs)
        all_signals = signal_engine.get_top_signals(500)  # Get maximum available

        # Convert to JSON-serializable format and apply filters
        signals_data = []
        for signal in all_signals:
            signal_dict = asdict(signal)
            signal_dict['timestamp'] = signal.timestamp.isoformat()

            # Apply filters
            if signal_type_filter and signal_dict.get('signal_type', '') != signal_type_filter:
                continue
            if signal_dict.get('confidence', 0) < min_confidence:
                continue
            if hasattr(signal, 'volume_24h') and signal.volume_24h < min_volume:
                continue

            signals_data.append(signal_dict)

        # Sort based on specified criteria
        if sort_by == 'volume':
            sorted_signals = sorted(signals_data, key=lambda x: getattr(x, 'volume_24h', 0), reverse=True)
        elif sort_by == 'change_24h':
            sorted_signals = sorted(signals_data, key=lambda x: abs(getattr(x, 'change_24h', 0)), reverse=True)
        else:  # Default to confidence
            sorted_signals = sorted(signals_data, key=lambda x: x.get('confidence', 0), reverse=True)

        # Apply limit after sorting
        limited_signals = sorted_signals[:limit]

        # Calculate total available signals
        total_available = len(signals_data)

        # Enhanced stats with market coverage info
        enhanced_stats = signal_engine.stats.copy()
        enhanced_stats['market_coverage'] = {
            'total_pairs_analyzed': enhanced_stats.get('total_analyzed', 0),
            'signals_generated': total_available,
            'coverage_percentage': round((total_available / max(enhanced_stats.get('total_analyzed', 1), 1)) * 100, 2)
        }

        # Return exact format as specified
        return jsonify({
            "success": True,
            "data": limited_signals,
            "count": len(limited_signals),
            "total_available": total_available,
            "limit": limit,
            "stats": enhanced_stats
        })

    except Exception as e:
        logger.error(f"[API] Error getting signals: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/pair/<symbol>')
def get_pair_analysis(symbol):
    """API endpoint untuk analisis pair tertentu"""
    try:
        # Get pair data
        ticker_data = signal_engine.binance_client.get_24hr_ticker()
        if not ticker_data:
            return jsonify({'success': False, 'error': 'Unable to fetch ticker data'}), 500

        # Find specific pair
        pair_data = None
        for ticker in ticker_data:
            if ticker['symbol'] == symbol.upper():
                pair_data = ticker
                break

        if not pair_data:
            return jsonify({'success': False, 'error': f'Pair {symbol} not found'}), 404

        # Create TradingPair object
        pair = TradingPair(
            symbol=pair_data['symbol'],
            base_asset=pair_data['symbol'][:-4],
            quote_asset='USDT',
            price=float(pair_data['lastPrice']),
            volume_24h=float(pair_data['quoteVolume']),
            change_24h=float(pair_data['priceChangePercent']),
            high_24h=float(pair_data['highPrice']),
            low_24h=float(pair_data['lowPrice']),
            last_update=datetime.now()
        )

        # Analyze pair
        signal = signal_engine.analyze_pair(pair)
        if not signal:
            return jsonify({'success': False, 'error': 'Unable to analyze pair'}), 500

        # Convert to JSON-serializable format
        signal_dict = asdict(signal)
        signal_dict['timestamp'] = signal.timestamp.isoformat()

        return jsonify({
            'success': True,
            'data': signal_dict
        })

    except Exception as e:
        logger.error(f"[API] Error analyzing pair {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """API endpoint untuk status sistem"""
    try:
        binance_healthy = signal_engine.binance_client.health_check()
        ai_available = signal_engine.ai_generator.model is not None

        with analysis_lock:
            analysis_info = analysis_status.copy()

        return jsonify({
            'success': True,
            'status': {
                'binance_api': 'healthy' if binance_healthy else 'error',
                'ai_service': 'available' if ai_available else 'unavailable',
                'uptime': time.time(),
                'stats': signal_engine.stats
            },
            'analysis_status': analysis_info
        })

    except Exception as e:
        logger.error(f"[API] Error getting status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-analysis', methods=['POST'])
def start_analysis():
    """API endpoint untuk memulai analisis manual"""
    try:
        with analysis_lock:
            if analysis_status['is_running']:
                return jsonify({
                    'success': False,
                    'error': 'Analisis sedang berjalan. Harap tunggu hingga selesai.'
                }), 400

            # Reset status
            analysis_status.update({
                'is_running': True,
                'progress': 0,
                'total_pairs': 0,
                'current_pair': '',
                'start_time': datetime.now(),
                'estimated_completion': None,
                'last_update': datetime.now(),
                'error_message': None
            })

        # Start analysis in background thread
        analysis_thread = threading.Thread(target=run_manual_analysis, daemon=True)
        analysis_thread.start()

        logger.info("[MANUAL] Manual analysis started by user request")

        return jsonify({
            'success': True,
            'message': 'Analisis dimulai. Gunakan /api/analysis-progress untuk memantau progress.',
            'estimated_duration': '4-5 menit untuk 449 pairs'
        })

    except Exception as e:
        logger.error(f"[API] Error starting analysis: {e}")
        with analysis_lock:
            analysis_status['is_running'] = False
            analysis_status['error_message'] = str(e)
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analysis-progress')
def get_analysis_progress():
    """API endpoint untuk mendapatkan progress analisis"""
    try:
        with analysis_lock:
            progress_info = analysis_status.copy()

        # Calculate estimated completion time
        if progress_info['is_running'] and progress_info['start_time'] and progress_info['progress'] > 0:
            elapsed = (datetime.now() - progress_info['start_time']).total_seconds()
            if progress_info['total_pairs'] > 0:
                estimated_total = (elapsed / progress_info['progress']) * progress_info['total_pairs']
                estimated_completion = progress_info['start_time'] + timedelta(seconds=estimated_total)
                progress_info['estimated_completion'] = estimated_completion.isoformat()

        return jsonify({
            'success': True,
            'progress': progress_info
        })

    except Exception as e:
        logger.error(f"[API] Error getting analysis progress: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/chart-data/<symbol>')
def get_chart_data(symbol):
    """Get historical chart data for a symbol"""
    try:
        # Get query parameters
        interval = request.args.get('interval', '1h')  # Default 1 hour
        limit = int(request.args.get('limit', 100))    # Default 100 candles

        logger.info(f"Fetching chart data for {symbol} with interval {interval}")

        # Validate interval
        valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
        if interval not in valid_intervals:
            return jsonify({
                'success': False,
                'error': f'Invalid interval. Valid intervals: {valid_intervals}'
            }), 400

        # Limit the number of candles to prevent excessive data
        limit = min(limit, 1000)

        # Get data from Binance API
        chart_data = get_binance_chart_data(symbol, interval, limit)

        if chart_data:
            return jsonify({
                'success': True,
                'data': chart_data,
                'symbol': symbol,
                'interval': interval,
                'count': len(chart_data)
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to fetch chart data from Binance'
            }), 500

    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market-data/<symbol>')
def get_market_data(symbol):
    """Get current market data for a symbol"""
    try:
        logger.info(f"Fetching market data for {symbol}")

        # Get current ticker data
        market_data = get_binance_market_data(symbol)

        if market_data:
            return jsonify({
                'success': True,
                'data': market_data
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to fetch market data from Binance'
            }), 500

    except Exception as e:
        logger.error(f"Error getting market data for {symbol}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/technical-indicators/<symbol>')
def get_technical_indicators(symbol):
    """Get technical indicators for a symbol"""
    try:
        interval = request.args.get('interval', '1h')
        logger.info(f"Fetching technical indicators for {symbol} with interval {interval}")

        # Get technical analysis data
        technical_data = get_binance_technical_indicators(symbol, interval)

        if technical_data:
            return jsonify({
                'success': True,
                'data': technical_data
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to calculate technical indicators'
            }), 500

    except Exception as e:
        logger.error(f"Error getting technical indicators for {symbol}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/pairs')
def get_pairs():
    """API endpoint untuk mendapatkan daftar trading pairs"""
    try:
        limit = request.args.get('limit', 100, type=int)
        limit = min(limit, 500)  # Maximum 500 pairs

        pairs = signal_engine.binance_client.get_top_pairs(limit)

        # Convert to JSON-serializable format
        pairs_data = []
        for pair in pairs:
            pair_dict = asdict(pair)
            if pair.last_update:
                pair_dict['last_update'] = pair.last_update.isoformat()
            pairs_data.append(pair_dict)

        return jsonify({
            'success': True,
            'data': pairs_data,
            'count': len(pairs_data)
        })

    except Exception as e:
        logger.error(f"[API] Error getting pairs: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/candlesticks/<symbol>')
def get_candlesticks(symbol):
    """API endpoint untuk mendapatkan candlestick data"""
    try:
        interval = request.args.get('interval', '1h')
        limit = request.args.get('limit', 100, type=int)
        limit = min(limit, 500)  # Maximum 500 candles

        candlesticks = signal_engine.binance_client.get_candlestick_data(symbol.upper(), interval, limit)

        # Convert to JSON-serializable format
        candles_data = []
        for candle in candlesticks:
            candle_dict = asdict(candle)
            candle_dict['open_time'] = candle.open_time.isoformat()
            candle_dict['close_time'] = candle.close_time.isoformat()
            candles_data.append(candle_dict)

        return jsonify({
            'success': True,
            'data': candles_data,
            'count': len(candles_data),
            'symbol': symbol.upper(),
            'interval': interval
        })

    except Exception as e:
        logger.error(f"[API] Error getting candlesticks for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/ai-analysis', methods=['POST'])
def ai_analysis():
    """API endpoint untuk AI analysis dengan Gemini"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', '')

        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        # Check if Gemini AI is available
        if not GENAI_AVAILABLE:
            return jsonify({
                'success': True,
                'data': {
                    'analysis': f"""
🤖 **DEMO AI ANALYSIS untuk {symbol}**

📊 **Technical Analysis:**
- Current Price: Market price
- Trend: Analyzing market momentum
- Support: Key support levels identified
- Resistance: Important resistance zones

🎯 **Trading Recommendation:**
- Entry: Consider entry around current levels
- Stop Loss: Set stop loss at 5% below entry
- Take Profit: Target 8% above entry
- Confidence: 75%

⚠️ **Risk Assessment:**
- Market volatility: Moderate
- Liquidity: Good
- Risk Level: Medium

📈 **Price Targets:**
- Short term: +3-5%
- Medium term: +8-12%
- Long term: Monitor market conditions

*Note: This is a demo analysis. For live AI analysis, configure Gemini API key.*

**Signal Analyzer AI by BOBACHEESE**
                    """.strip(),
                    'confidence': 75,
                    'recommendation': 'HOLD',
                    'entry_price': 0,
                    'stop_loss': 0,
                    'take_profit': 0
                }
            })

        # If Gemini is available, use real AI analysis
        try:
            # Get market data for context
            market_data = get_binance_market_data(symbol)

            # Generate AI analysis
            analysis_text = signal_engine.ai_generator.generate_analysis(symbol, market_data)

            return jsonify({
                'success': True,
                'data': {
                    'analysis': analysis_text,
                    'confidence': 85,
                    'recommendation': 'BUY',
                    'entry_price': market_data.get('price', 0),
                    'stop_loss': market_data.get('price', 0) * 0.95,
                    'take_profit': market_data.get('price', 0) * 1.08
                }
            })

        except Exception as ai_error:
            logger.error(f"[AI] Error generating analysis for {symbol}: {ai_error}")
            return jsonify({
                'success': False,
                'error': 'AI analysis temporarily unavailable. Please check your Gemini API key and try again.'
            }), 500

    except Exception as e:
        logger.error(f"[API] Error in AI analysis: {e}")
        return jsonify({
            'success': False,
            'error': 'Analysis failed. Please check your Gemini API key and try again.'
        }), 500

# Static files
@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║              🚀 BINANCE SIGNAL GENERATOR PRO 🚀              ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📊 Real-time Technical Analysis & AI-Powered Signals       ║
    ║  🎯 Target: 500+ Binance Futures Pairs                      ║
    ║  🧠 60+ Technical Indicators + Google Gemini AI             ║
    ║  ⚡ Performance: <30s analysis, <2GB memory                 ║
    ║  🌐 Web Interface: http://localhost:5001                    ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  🔗 API: Binance Futures (Public endpoints only)            ║
    ║  🤖 AI: Google Gemini Pro untuk signal analysis             ║
    ║  📈 Charts: Chart.js dengan real-time updates               ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  Author: BOBACHEESE                                          ║
    ║  Framework: Flask + HTML/CSS/JavaScript                     ║
    ║  Environment: Conda on Windows 11                           ║
    ║  Port: 5001 (menghindari konflik dengan arbitrage bot)      ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def validate_environment():
    """Validate environment dan dependencies"""
    try:
        # Test imports
        import requests
        import pandas as pd
        import numpy as np
        import ta
        from flask import Flask
        import google.generativeai as genai

        logger.info("[OK] All dependencies available")

        # Test Binance API
        if signal_engine.binance_client.health_check():
            logger.info("[OK] Binance API accessible")
        else:
            logger.warning("[WARNING] Binance API not accessible")

        # Test AI service
        if CONFIG['ai']['api_key']:
            logger.info("[OK] Gemini API key configured")
        else:
            logger.warning("[WARNING] Gemini API key not configured - AI features disabled")

        return True

    except ImportError as e:
        logger.error(f"[ERROR] Missing dependency: {e}")
        return False
    except Exception as e:
        logger.error(f"[ERROR] Environment validation failed: {e}")
        return False

def run_manual_analysis():
    """Function untuk menjalankan analisis manual dengan progress tracking"""
    try:
        logger.info("[MANUAL] Starting manual analysis...")

        # Get all trading pairs
        ticker_data = signal_engine.binance_client.get_24hr_ticker()
        if not ticker_data:
            raise Exception("Gagal mendapatkan data ticker dari Binance")

        # Filter USDT pairs
        usdt_pairs = [ticker for ticker in ticker_data if ticker['symbol'].endswith('USDT')]
        total_pairs = len(usdt_pairs)

        with analysis_lock:
            analysis_status['total_pairs'] = total_pairs
            analysis_status['last_update'] = datetime.now()

        logger.info(f"[MANUAL] Analyzing {total_pairs} USDT pairs...")

        signals = []
        processed = 0

        for ticker in usdt_pairs:
            try:
                symbol = ticker['symbol']

                with analysis_lock:
                    analysis_status['current_pair'] = symbol
                    analysis_status['progress'] = processed
                    analysis_status['last_update'] = datetime.now()

                # Generate signal for this pair
                signal = signal_engine.generate_signal(symbol)
                if signal:
                    signals.append(signal)

                processed += 1

                # Log progress every 50 pairs
                if processed % 50 == 0:
                    logger.info(f"[MANUAL] Progress: {processed}/{total_pairs} pairs analyzed")

                # Small delay to prevent rate limiting
                time.sleep(0.1)

            except Exception as e:
                logger.warning(f"[MANUAL] Error analyzing {symbol}: {e}")
                processed += 1
                continue

        # Analysis completed
        with analysis_lock:
            analysis_status.update({
                'is_running': False,
                'progress': processed,
                'current_pair': '',
                'last_update': datetime.now(),
                'error_message': None
            })

        logger.info(f"[MANUAL] Analysis completed! Generated {len(signals)} signals from {processed} pairs")

    except Exception as e:
        logger.error(f"[MANUAL] Error in manual analysis: {e}")
        with analysis_lock:
            analysis_status.update({
                'is_running': False,
                'error_message': str(e),
                'last_update': datetime.now()
            })

def background_signal_updater():
    """Background thread untuk update signals secara berkala - DISABLED untuk manual control"""
    # Background updater is disabled - analysis is now manual only
    logger.info("[BACKGROUND] Background signal updater disabled - using manual control")
    while True:
        time.sleep(3600)  # Sleep for 1 hour, effectively disabling auto-updates

if __name__ == '__main__':
    print_banner()

    if not validate_environment():
        logger.error("[FATAL] Environment validation failed")
        sys.exit(1)

    try:
        logger.info("[START] Starting Binance Signal Generator Pro...")

        # Start background updater (disabled for manual control)
        background_thread = threading.Thread(target=background_signal_updater, daemon=True)
        background_thread.start()
        logger.info("[BACKGROUND] Background updater started (manual control mode)")

        # Skip initial signal generation - wait for manual trigger
        logger.info("[INIT] Skipping automatic signal generation - waiting for manual trigger")
        logger.info("[INIT] Use the 'Mulai Analisis' button in the web interface to start analysis")

        # Start Flask app
        logger.info("[WEB] Starting web interface...")
        logger.info("[INFO] Access the application at: http://localhost:5001")
        logger.info("[INFO] Use Ctrl+C to stop the application")
        logger.info("[INFO] Status: Siap untuk analisis manual")

        app.run(
            host='0.0.0.0',
            port=int(os.getenv('FLASK_PORT', '5001')),
            debug=os.getenv('FLASK_DEBUG', 'False').lower() == 'true',
            threaded=True,
            use_reloader=False  # Disable reloader to prevent duplicate background threads
        )

    except KeyboardInterrupt:
        logger.info("[STOP] Application stopped by user")
    except Exception as e:
        logger.error(f"[FATAL] Application error: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info("[EXIT] Binance Signal Generator Pro shutdown complete")
