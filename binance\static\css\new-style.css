/* Binance Signal Generator Pro - New Pastel UI Design */
/* Clean, modern design with soft pastel colors and smooth animations */

/* CSS Variables - Pastel Color Palette */
:root {
    /* Primary Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-card: #ffffff;
    
    /* Pastel Colors */
    --pastel-blue: #a7c7e7;
    --pastel-green: #b8e6b8;
    --pastel-pink: #f4c2c2;
    --pastel-purple: #d1c4e9;
    --pastel-yellow: #fff3cd;
    --pastel-orange: #ffd4a3;
    
    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-white: #ffffff;
    
    /* Border & Shadow */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.15);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Transitions */
    --transition-fast: 200ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--pastel-blue), var(--pastel-purple));
    --gradient-success: linear-gradient(135deg, var(--pastel-green), var(--pastel-blue));
    --gradient-warning: linear-gradient(135deg, var(--pastel-yellow), var(--pastel-orange));
    --gradient-error: linear-gradient(135deg, var(--pastel-pink), var(--pastel-orange));
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Layout Container */
.app-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: var(--spacing-lg);
}

.main-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
}

/* Header */
.app-header {
    background: var(--gradient-primary);
    padding: var(--spacing-lg) var(--spacing-xl);
    color: var(--text-white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.nav-tab {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
    position: relative;
    min-width: 120px;
    text-align: center;
}

.nav-tab:hover {
    background: rgba(167, 199, 231, 0.1);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.nav-tab.active {
    background: var(--bg-card);
    color: var(--text-primary);
    font-weight: 600;
}

.nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px 2px 0 0;
}

/* Main Content Area */
.content-area {
    padding: var(--spacing-xl);
    min-height: 600px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

/* Card Components */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-title .icon {
    font-size: 1.5rem;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--text-white);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--text-primary);
}

.btn-error {
    background: var(--gradient-error);
    color: var(--text-white);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    from { transform: translateX(-100px); }
    to { transform: translateX(calc(100vw + 100px)); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Manual Analysis Panel */
.manual-analysis-panel {
    background: linear-gradient(135deg, rgba(167, 199, 231, 0.1), rgba(209, 196, 233, 0.1));
    border: 2px solid var(--pastel-blue);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.manual-analysis-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.analysis-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.status-badge.idle {
    background: rgba(167, 199, 231, 0.2);
    color: #4a90a4;
    border: 1px solid var(--pastel-blue);
}

.status-badge.running {
    background: rgba(255, 243, 205, 0.8);
    color: #b7791f;
    border: 1px solid var(--pastel-yellow);
    animation: pulse 2s infinite;
}

.status-badge.completed {
    background: rgba(184, 230, 184, 0.8);
    color: #2f7d32;
    border: 1px solid var(--pastel-green);
}

.status-badge.error {
    background: rgba(244, 194, 194, 0.8);
    color: #c62828;
    border: 1px solid var(--pastel-pink);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

.analysis-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.control-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.start-analysis-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.start-analysis-btn.analyzing {
    background: var(--gradient-warning);
    color: var(--text-primary);
}

.start-analysis-btn.completed {
    background: var(--gradient-success);
    color: var(--text-white);
}

.start-analysis-btn.error {
    background: var(--gradient-error);
    color: var(--text-white);
}

.analysis-info {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Progress Bar */
.progress-container {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    display: none;
    animation: slideDown 0.4s ease-out;
}

.progress-container.show {
    display: block;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.progress-text {
    font-weight: 500;
    color: var(--text-primary);
}

.progress-percentage {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width var(--transition-normal);
    border-radius: 6px;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Additional Animations */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: var(--spacing-md);
    }

    .app-header {
        padding: var(--spacing-lg);
    }

    .app-title {
        font-size: 2rem;
    }

    .content-area {
        padding: var(--spacing-lg);
    }

    .nav-tab {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }

    .manual-analysis-panel {
        padding: var(--spacing-lg);
    }

    .analysis-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .control-row {
        flex-direction: column;
        align-items: stretch;
    }

    .start-analysis-btn {
        min-width: auto;
        width: 100%;
    }

    .analysis-info {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* Signal Cards */
.signals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.signal-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.signal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.signal-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.signal-symbol {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.signal-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.signal-type.strong-buy {
    background: rgba(184, 230, 184, 0.8);
    color: #2e7d32;
}

.signal-type.buy {
    background: rgba(167, 199, 231, 0.8);
    color: #1976d2;
}

.signal-type.hold {
    background: rgba(255, 243, 205, 0.8);
    color: #f57c00;
}

.signal-type.sell {
    background: rgba(255, 212, 163, 0.8);
    color: #e64a19;
}

.signal-type.strong-sell {
    background: rgba(244, 194, 194, 0.8);
    color: #d32f2f;
}

.signal-metrics {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.metric {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.metric-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
}

.metric-value {
    font-size: 1rem;
    font-weight: 600;
}

.metric-value.high-confidence {
    color: #2e7d32;
}

.metric-value.medium-confidence {
    color: #f57c00;
}

.metric-value.low-confidence {
    color: #d32f2f;
}

.signal-prices {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.price-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: center;
}

.price-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
}

.price-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    font-family: monospace;
}

.signal-reasoning {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(167, 199, 231, 0.1);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--pastel-blue);
}

.signal-timestamp {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: right;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* Loading Placeholder */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--pastel-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Settings */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.setting-group {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.setting-group h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: 1.1rem;
}

.setting-item {
    margin-bottom: var(--spacing-md);
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.setting-item input[type="number"] {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: border-color var(--transition-normal);
}

.setting-item input[type="number"]:focus {
    outline: none;
    border-color: var(--pastel-blue);
    box-shadow: 0 0 0 3px rgba(167, 199, 231, 0.2);
}

.setting-item input[type="checkbox"] {
    margin-right: var(--spacing-sm);
}

/* About Section */
.about-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.about-section {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.about-section h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.about-section ul {
    list-style: none;
    padding: 0;
}

.about-section li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.about-section li:last-child {
    border-bottom: none;
}

.about-section li::before {
    content: '✓';
    color: var(--pastel-green);
    font-weight: bold;
    margin-right: var(--spacing-sm);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-primary);
    color: var(--text-white);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all var(--transition-normal);
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Gemini API Configuration */
.api-config-section {
    background: linear-gradient(135deg, rgba(167, 199, 231, 0.1), rgba(209, 196, 233, 0.1));
    border: 2px solid var(--pastel-purple);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.api-config-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.api-config-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.api-status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.api-status-indicator.connected {
    background: rgba(184, 230, 184, 0.8);
    color: #2f7d32;
    border: 1px solid var(--pastel-green);
}

.api-status-indicator.disconnected {
    background: rgba(244, 194, 194, 0.8);
    color: #c62828;
    border: 1px solid var(--pastel-pink);
}

.api-status-indicator.testing {
    background: rgba(255, 243, 205, 0.8);
    color: #b7791f;
    border: 1px solid var(--pastel-yellow);
    animation: pulse 2s infinite;
}

.api-input-group {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    align-items: flex-end;
}

.api-input-field {
    flex: 1;
}

.api-input-field label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.api-input-field input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: all var(--transition-normal);
    background: var(--bg-card);
}

.api-input-field input:focus {
    outline: none;
    border-color: var(--pastel-purple);
    box-shadow: 0 0 0 3px rgba(209, 196, 233, 0.2);
}

.api-input-field input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.verify-api-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 120px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.verify-api-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.verify-api-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.verify-api-btn.testing {
    background: var(--gradient-warning);
    color: var(--text-primary);
}

.verify-api-btn.success {
    background: var(--gradient-success);
    color: var(--text-white);
}

.verify-api-btn.error {
    background: var(--gradient-error);
    color: var(--text-white);
}

/* AI Analysis Button on Signal Cards */
.ai-analysis-btn {
    background: linear-gradient(135deg, var(--pastel-purple), var(--pastel-pink));
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: center;
    position: relative;
}

.ai-analysis-btn::after {
    content: '📊';
    position: absolute;
    right: var(--spacing-sm);
    opacity: 0.8;
    font-size: 0.9rem;
}

.ai-analysis-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.ai-analysis-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.ai-analysis-btn.loading {
    background: var(--gradient-warning);
    color: var(--text-primary);
}

.ai-analysis-btn .loading-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* AI Analysis Modal */
.ai-analysis-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-lg);
}

.ai-analysis-modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.ai-analysis-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: slideInUp 0.4s ease-out;
}

.ai-analysis-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gradient-primary);
    color: var(--text-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.ai-analysis-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: background var(--transition-normal);
}

.close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.ai-analysis-body {
    padding: var(--spacing-xl);
}

.ai-analysis-loading {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.ai-analysis-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--pastel-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

.ai-analysis-result {
    line-height: 1.6;
    color: var(--text-secondary);
}

.ai-analysis-result h3 {
    color: var(--text-primary);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.ai-analysis-result p {
    margin-bottom: var(--spacing-md);
}

.ai-analysis-result strong {
    color: var(--text-primary);
}

/* Additional Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chart Container */
.chart-container {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    position: relative;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.timeframe-selector {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--bg-secondary);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
}

.timeframe-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: all var(--transition-normal);
}

.timeframe-btn:hover {
    background: var(--pastel-blue);
    color: var(--text-white);
}

.timeframe-btn.active {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.chart-toggle-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.chart-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.chart-canvas-container {
    position: relative;
    height: 400px;
    width: 100%;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    z-index: 10;
}

.chart-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--pastel-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.chart-error {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.chart-error .error-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* AI Overlay Styles */
.ai-overlay-controls {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.ai-overlay-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.ai-overlay-toggle input[type="checkbox"] {
    margin-right: var(--spacing-xs);
}

.ai-overlay-legend {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-sm);
    font-size: 0.8rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.legend-color {
    width: 12px;
    height: 3px;
    border-radius: 2px;
}

.legend-color.entry { background: #00ff00; }
.legend-color.stop-loss { background: #ff0000; }
.legend-color.take-profit { background: #0066ff; }
.legend-color.prediction { background: #ffaa00; border-style: dashed; }

/* Chart Fullscreen Mode */
.chart-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    background: var(--bg-card);
    padding: var(--spacing-lg);
}

.chart-fullscreen .chart-canvas-container {
    height: calc(100vh - 200px);
}

.fullscreen-controls {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    gap: var(--spacing-sm);
}

.fullscreen-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Signal Counter */
.signal-counter {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.signal-counter.updating {
    opacity: 0.6;
    transform: scale(0.98);
}

/* Configuration Slider */
.config-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-secondary);
    outline: none;
    margin-top: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.config-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--gradient-primary);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.config-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.config-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--gradient-primary);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

/* Trading Simulator Integration */
.trading-simulator-container {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.simulator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.simulator-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.simulator-toggle-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.simulator-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.simulator-content {
    transition: all var(--transition-normal);
}

.simulator-content.collapsed {
    display: none;
}

/* Fixed Trading Simulator Layout */
.simulator-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    align-items: start;
}

.simulator-section {
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    height: fit-content;
}

.simulator-section h5 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    margin-bottom: var(--spacing-md);
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--pastel-blue);
    box-shadow: 0 0 0 3px rgba(167, 199, 231, 0.2);
}

.form-control.valid {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.05);
}

.form-control.invalid {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

.form-control.loading {
    background-image: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.form-validation-message {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 0.75rem;
    color: #ef4444;
    opacity: 0;
    transform: translateY(-5px);
    transition: all var(--transition-fast);
}

.form-validation-message.show {
    opacity: 1;
    transform: translateY(0);
}

.form-validation-message.success {
    color: #22c55e;
}

.input-tooltip {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.input-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

.form-group:hover .input-tooltip {
    opacity: 1;
}

.radio-group {
    display: flex;
    gap: var(--spacing-md);
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    margin-bottom: 0;
}

/* Enhanced Position Info */
.position-info {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.info-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.info-item span:first-child {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.info-item span:last-child {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
}

/* Enhanced Simulator Actions */
.simulator-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.simulator-actions .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    font-size: 0.9rem;
    border-radius: var(--border-radius);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.simulator-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.simulator-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-success {
    background: linear-gradient(135deg, #4ade80, #22c55e);
    color: white;
    border: none;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: linear-gradient(135deg, #f87171, #ef4444);
    color: white;
    border: none;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Enhanced Gemini Legend */
.gemini-legend {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    font-size: 0.85rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--transition-normal);
}

.gemini-legend.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.gemini-legend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.gemini-legend-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.gemini-legend-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.gemini-legend-controls input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.gemini-legend-controls label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.gemini-legend-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.gemini-legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.gemini-legend-item:hover {
    background: var(--bg-secondary);
}

.gemini-legend-color {
    width: 20px;
    height: 3px;
    border-radius: 2px;
    position: relative;
}

.gemini-legend-color.entry {
    background: #00ff00;
    box-shadow: 0 0 4px rgba(0, 255, 0, 0.3);
}

.gemini-legend-color.stop {
    background: #ff0000;
    box-shadow: 0 0 4px rgba(255, 0, 0, 0.3);
}

.gemini-legend-color.profit {
    background: #0066ff;
    box-shadow: 0 0 4px rgba(0, 102, 255, 0.3);
}

.gemini-legend-color.trend {
    background: #ff8800;
    background-image: repeating-linear-gradient(
        90deg,
        #ff8800,
        #ff8800 5px,
        transparent 5px,
        transparent 10px
    );
    box-shadow: 0 0 4px rgba(255, 136, 0, 0.3);
}

.gemini-legend-info {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.75rem;
    font-style: italic;
}

/* Signal List Transitions */
.signal-card {
    transition: all 300ms ease-in-out;
}

.signal-list {
    transition: opacity 300ms ease-in-out;
}

.signals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    transition: all var(--transition-normal);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: var(--border-radius);
}

.loading-overlay .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--pastel-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Fade-in Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.signal-card.fade-in {
    animation: fadeIn 300ms ease-out;
}

/* Responsive Design for AI Features */
@media (max-width: 768px) {
    .api-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .verify-api-btn {
        width: 100%;
        margin-top: var(--spacing-sm);
    }

    .ai-analysis-modal {
        padding: var(--spacing-md);
    }

    .ai-analysis-content {
        max-height: 95vh;
    }

    .ai-analysis-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .ai-analysis-body {
        padding: var(--spacing-lg);
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .chart-controls {
        width: 100%;
        justify-content: space-between;
    }

    .timeframe-selector {
        flex-wrap: wrap;
    }

    .chart-canvas-container {
        height: 300px;
    }

    .chart-fullscreen .chart-canvas-container {
        height: calc(100vh - 150px);
    }

    .signals-grid {
        grid-template-columns: 1fr;
    }

    .simulator-actions {
        flex-direction: column;
    }

    .gemini-legend {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .signal-counter {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .simulator-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .simulator-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .simulator-toggle-btn {
        width: 100%;
        justify-content: center;
    }

    .trading-simulator-container {
        margin-top: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .gemini-legend-items {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .gemini-legend-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .gemini-legend-controls {
        width: 100%;
        justify-content: center;
    }
}
