<!DOCTYPE html>
<html>
<head>
    <title>Force Fix Loading Screen</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .btn { margin: 10px; padding: 10px 20px; background: #333; color: #0f0; border: 1px solid #0f0; cursor: pointer; }
        .btn:hover { background: #0f0; color: #000; }
        .log { margin: 2px 0; font-size: 12px; }
        .error { color: #f00; }
        .success { color: #0f0; }
        .info { color: #0ff; }
    </style>
</head>
<body>
    <h1>🔧 Force Fix Loading Screen</h1>
    
    <button class="btn" onclick="forceFixMainPage()">🚀 Force Fix Main Page</button>
    <button class="btn" onclick="openMainPageDirect()">🌐 Open Main Page Direct</button>
    <button class="btn" onclick="testJavaScriptInjection()">💉 Test JavaScript Injection</button>
    
    <div id="log"></div>
    
    <script>
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(div);
            console.log(message);
        }
        
        function forceFixMainPage() {
            addLog('🔧 Attempting to force fix main page...', 'info');
            
            // Create iframe to access main page
            const iframe = document.createElement('iframe');
            iframe.src = '/';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeWindow = iframe.contentWindow;
                    
                    addLog('✅ Iframe loaded successfully', 'success');
                    
                    // Force hide loading screen
                    const loadingScreen = iframeDoc.getElementById('loading-screen');
                    const mainContainer = iframeDoc.getElementById('main-container');
                    
                    if (loadingScreen) {
                        addLog('📱 Found loading screen, forcing hide...', 'info');
                        loadingScreen.style.display = 'none';
                        loadingScreen.style.opacity = '0';
                        addLog('✅ Loading screen hidden', 'success');
                    }
                    
                    if (mainContainer) {
                        addLog('📱 Found main container, forcing show...', 'info');
                        mainContainer.style.display = 'flex';
                        mainContainer.style.opacity = '1';
                        addLog('✅ Main container shown', 'success');
                    }
                    
                    // Force create app instance if not exists
                    if (!iframeWindow.binanceApp) {
                        addLog('🚀 App instance not found, attempting to create...', 'info');
                        
                        // Inject script to force create app
                        const script = iframeDoc.createElement('script');
                        script.textContent = `
                            console.log('[FORCE] Creating BinanceSignalApp instance...');
                            try {
                                if (typeof BinanceSignalApp !== 'undefined') {
                                    window.binanceApp = new BinanceSignalApp();
                                    console.log('[FORCE] App instance created successfully');
                                } else {
                                    console.error('[FORCE] BinanceSignalApp class not found');
                                }
                            } catch (error) {
                                console.error('[FORCE] Error creating app instance:', error);
                            }
                        `;
                        iframeDoc.head.appendChild(script);
                        
                        setTimeout(() => {
                            if (iframeWindow.binanceApp) {
                                addLog('✅ App instance created successfully', 'success');
                                
                                // Force initialize manual analysis UI
                                if (typeof iframeWindow.binanceApp.updateAnalysisUI === 'function') {
                                    iframeWindow.binanceApp.updateAnalysisUI('idle');
                                    addLog('✅ Manual analysis UI initialized', 'success');
                                }
                            } else {
                                addLog('❌ Failed to create app instance', 'error');
                            }
                        }, 1000);
                    } else {
                        addLog('✅ App instance already exists', 'success');
                    }
                    
                    // Test manual analysis elements
                    setTimeout(() => {
                        const btn = iframeDoc.getElementById('start-analysis-btn');
                        const statusText = iframeDoc.querySelector('.status-text');
                        const statusIndicator = iframeDoc.getElementById('analysis-status-indicator');
                        
                        addLog(`Button: ${!!btn}`, btn ? 'success' : 'error');
                        addLog(`Status Text: ${!!statusText}`, statusText ? 'success' : 'error');
                        addLog(`Status Indicator: ${!!statusIndicator}`, statusIndicator ? 'success' : 'error');
                        
                        if (btn) {
                            addLog(`Button text: "${btn.textContent.trim()}"`, 'info');
                            addLog(`Button disabled: ${btn.disabled}`, 'info');
                        }
                        
                        addLog('🎯 Force fix completed! Check main page.', 'success');
                    }, 2000);
                    
                } catch (error) {
                    addLog(`❌ Error accessing iframe: ${error.message}`, 'error');
                }
            };
            
            iframe.onerror = function() {
                addLog('❌ Failed to load iframe', 'error');
            };
        }
        
        function openMainPageDirect() {
            addLog('🌐 Opening main page directly...', 'info');
            window.open('/', '_blank');
        }
        
        function testJavaScriptInjection() {
            addLog('💉 Testing JavaScript injection...', 'info');
            
            // Try to inject script directly into main page
            fetch('/')
                .then(response => response.text())
                .then(html => {
                    addLog('✅ Main page HTML fetched', 'success');
                    
                    // Create new window with modified HTML
                    const newWindow = window.open('', '_blank');
                    
                    // Inject force fix script
                    const modifiedHtml = html.replace(
                        '</head>',
                        `
                        <script>
                            console.log('[INJECT] Force fix script injected');
                            
                            setTimeout(() => {
                                console.log('[INJECT] Forcing loading screen hide...');
                                const loadingScreen = document.getElementById('loading-screen');
                                const mainContainer = document.getElementById('main-container');
                                
                                if (loadingScreen) {
                                    loadingScreen.style.display = 'none';
                                    console.log('[INJECT] Loading screen hidden');
                                }
                                
                                if (mainContainer) {
                                    mainContainer.style.display = 'flex';
                                    console.log('[INJECT] Main container shown');
                                }
                                
                                // Force initialize manual analysis
                                setTimeout(() => {
                                    if (window.binanceApp && typeof window.binanceApp.updateAnalysisUI === 'function') {
                                        window.binanceApp.updateAnalysisUI('idle');
                                        console.log('[INJECT] Manual analysis UI initialized');
                                    }
                                }, 1000);
                                
                            }, 500);
                        </script>
                        </head>`
                    );
                    
                    newWindow.document.write(modifiedHtml);
                    newWindow.document.close();
                    
                    addLog('✅ Modified page opened in new window', 'success');
                })
                .catch(error => {
                    addLog(`❌ Error fetching main page: ${error.message}`, 'error');
                });
        }
        
        addLog('🔧 Force fix tool loaded', 'success');
        addLog('Click buttons above to attempt fixes', 'info');
        
    </script>
</body>
</html>
