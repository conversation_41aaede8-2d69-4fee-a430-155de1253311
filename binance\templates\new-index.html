<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance Signal Generator Pro - New UI</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- New CSS -->
    <link rel="stylesheet" href="/static/css/new-style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
</head>
<body>
    <div class="app-container">
        <div class="main-wrapper">
            <!-- Header -->
            <header class="app-header">
                <h1 class="app-title">🚀 Binance Signal Generator Pro</h1>
                <p class="app-subtitle">Advanced Trading Signal Analysis with AI-Powered Insights</p>
            </header>
            
            <!-- Navigation Tabs -->
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="signals">
                    📊 Trading Signals
                </button>
                <button class="nav-tab" data-tab="analysis">
                    🔍 Manual Analysis
                </button>
                <button class="nav-tab" data-tab="settings">
                    ⚙️ Settings
                </button>
                <button class="nav-tab" data-tab="about">
                    ℹ️ About
                </button>
            </nav>
            
            <!-- Main Content Area -->
            <main class="content-area">
                <!-- Trading Signals Tab -->
                <div id="signals-tab" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">📈</span>
                                Latest Trading Signals
                            </h2>
                            <button id="refresh-signals" class="btn btn-primary">
                                <span>🔄</span>
                                Refresh
                            </button>
                        </div>
                        
                        <!-- Signals Container -->
                        <div id="signals-container" class="signals-grid">
                            <!-- Signals will be loaded here -->
                            <div class="loading-placeholder">
                                <div class="loading-spinner"></div>
                                <p>Loading signals...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Analysis Tab -->
                <div id="analysis-tab" class="tab-content">
                    <!-- Manual Analysis Panel -->
                    <div class="manual-analysis-panel">
                        <div class="analysis-header">
                            <h2 class="analysis-title">
                                <span class="icon">🤖</span>
                                Manual Analysis Control
                            </h2>
                            <div class="status-badge idle">
                                <span class="status-indicator"></span>
                                Ready for analysis
                            </div>
                        </div>
                        
                        <div class="analysis-controls">
                            <div class="control-row">
                                <button id="start-analysis-btn" class="btn start-analysis-btn">
                                    <span class="btn-icon">🚀</span>
                                    <span class="btn-text">Start Analysis</span>
                                </button>
                                
                                <div class="analysis-info">
                                    <div class="info-item">
                                        <span class="info-label">Total Pairs</span>
                                        <span class="info-value" id="total-pairs">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Signals Generated</span>
                                        <span class="info-value" id="signals-generated">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Last Update</span>
                                        <span class="info-value" id="last-update">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Container -->
                        <div id="progress-container" class="progress-container">
                            <div class="progress-header">
                                <span id="progress-text" class="progress-text">Analyzing...</span>
                                <span id="progress-percentage" class="progress-percentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div id="progress-fill" class="progress-fill"></div>
                            </div>
                            <div class="progress-details">
                                <span>Current: <span id="current-pair">-</span></span>
                                <span id="estimated-time">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Analysis Statistics -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <span class="icon">📊</span>
                                Analysis Statistics
                            </h3>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-icon">🌐</div>
                                <div class="stat-content">
                                    <div class="stat-label">Binance API</div>
                                    <div class="stat-value" id="binance-api-status">🟢</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">🤖</div>
                                <div class="stat-content">
                                    <div class="stat-label">AI Service</div>
                                    <div class="stat-value" id="ai-service-status">🟡</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">⏱️</div>
                                <div class="stat-content">
                                    <div class="stat-label">Avg Analysis Time</div>
                                    <div class="stat-value">~4 min</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">🎯</div>
                                <div class="stat-content">
                                    <div class="stat-label">Success Rate</div>
                                    <div class="stat-value">85%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div id="settings-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">⚙️</span>
                                Application Settings
                            </h2>
                        </div>
                        
                        <div class="settings-content">
                            <div class="setting-group">
                                <h3>Analysis Configuration</h3>
                                <div class="setting-item">
                                    <label>Auto-refresh interval (seconds)</label>
                                    <input type="number" value="30" min="10" max="300">
                                </div>
                                <div class="setting-item">
                                    <label>Maximum signals to display</label>
                                    <input type="number" value="50" min="10" max="200">
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Notification Preferences</h3>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" checked>
                                        Show analysis completion notifications
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" checked>
                                        Show error notifications
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- About Tab -->
                <div id="about-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">ℹ️</span>
                                About Binance Signal Generator Pro
                            </h2>
                        </div>
                        
                        <div class="about-content">
                            <div class="about-section">
                                <h3>🚀 Features</h3>
                                <ul>
                                    <li>Real-time trading signal analysis</li>
                                    <li>AI-powered market insights</li>
                                    <li>Manual analysis control</li>
                                    <li>Beautiful, responsive UI</li>
                                    <li>Comprehensive progress tracking</li>
                                </ul>
                            </div>
                            
                            <div class="about-section">
                                <h3>👨‍💻 Developer</h3>
                                <p><strong>BOBACHEESE</strong></p>
                                <div class="social-links">
                                    <a href="#" class="social-link">🐙 GitHub</a>
                                    <a href="#" class="social-link">🐦 Twitter</a>
                                    <a href="#" class="social-link">💼 LinkedIn</a>
                                </div>
                            </div>
                            
                            <div class="about-section">
                                <h3>📝 Version Info</h3>
                                <p>Version: 2.0.0 (New UI)</p>
                                <p>Last Updated: {{ current_date }}</p>
                                <p>Built with: Flask, Python, Modern CSS, Vanilla JS</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- New JavaScript -->
    <script src="/static/js/new-app.js"></script>
</body>
</html>
