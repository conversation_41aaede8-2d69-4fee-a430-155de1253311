<!DOCTYPE html>
<html>
<head>
    <title>Test Manual Analysis</title>
</head>
<body>
    <h1>Test Manual Analysis API</h1>
    
    <button onclick="startAnalysis()">Start Manual Analysis</button>
    <button onclick="checkProgress()">Check Progress</button>
    
    <div id="result"></div>
    
    <script>
        async function startAnalysis() {
            try {
                const response = await fetch('/api/start-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function checkProgress() {
            try {
                const response = await fetch('/api/analysis-progress');
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
