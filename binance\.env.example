# Binance Signal Prompt Generator Pro - Environment Configuration
# Copy this file to .env and fill in your API keys

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Application Configuration
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_PORT=5001

# Binance API Configuration (Public endpoints only)
BINANCE_BASE_URL=https://fapi.binance.com
BINANCE_RATE_LIMIT=1200
BINANCE_TIMEOUT=10

# Performance Configuration
MAX_WORKERS=20
CACHE_EXPIRY=300
MAX_MEMORY_USAGE=2048
TARGET_PAIRS=500

# UI Configuration
UI_THEME=dark_futuristic
UI_LANGUAGE=id
AUTO_REFRESH=True
NOTIFICATION_THRESHOLD=5.0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=binance_signals.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# Author Information
AUTHOR_NAME=BOBACHEESE
AUTHOR_GITHUB=https://github.com/bobacheese
AUTHOR_YOUTUBE=https://youtube.com/@bobacheese?si=5M2leEilS3_VmNS6
AUTHOR_COFFEE=coff.ee/amarullohzd
