<!DOCTYPE html>
<html>
<head>
    <title>Final Manual Analysis Test</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 2px 0; font-size: 12px; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
        .test-section { border: 1px solid #333; margin: 10px 0; padding: 10px; }
        .btn { margin: 5px; padding: 8px 16px; background: #333; color: #0f0; border: 1px solid #0f0; cursor: pointer; }
        .btn:hover { background: #0f0; color: #000; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #333; }
    </style>
</head>
<body>
    <h1>🎯 Final Manual Analysis Test</h1>
    
    <div class="test-section">
        <h3>🚀 Test Controls</h3>
        <button class="btn" onclick="runCompleteTest()">🔍 Run Complete Test</button>
        <button class="btn" onclick="testUIOnly()">🎨 Test UI Only</button>
        <button class="btn" onclick="testClickSimulation()">👆 Test Click Simulation</button>
        <button class="btn" onclick="openMainPageTest()">🌐 Open Main Page Test</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        const result = document.getElementById('result');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            result.appendChild(div);
            result.scrollTop = result.scrollHeight;
            console.log(`[TEST] ${message}`);
        }
        
        function clearResult() {
            result.innerHTML = '';
        }
        
        async function testUIOnly() {
            clearResult();
            log('=== UI ELEMENTS TEST ===', 'info');
            
            // Navigate to main page first
            window.location.href = '/';
            
            // Wait for page load
            setTimeout(() => {
                const btn = document.getElementById('start-analysis-btn');
                const statusText = document.querySelector('.status-text');
                const statusIndicator = document.getElementById('analysis-status-indicator');
                const progressContainer = document.getElementById('progress-container');
                
                log(`Button found: ${!!btn}`, btn ? 'success' : 'error');
                log(`Status text found: ${!!statusText}`, statusText ? 'success' : 'error');
                log(`Status indicator found: ${!!statusIndicator}`, statusIndicator ? 'success' : 'error');
                log(`Progress container found: ${!!progressContainer}`, progressContainer ? 'success' : 'error');
                
                if (btn) {
                    log(`Button text: "${btn.textContent.trim()}"`, 'info');
                    log(`Button disabled: ${btn.disabled}`, 'info');
                    log(`Button className: "${btn.className}"`, 'info');
                    
                    const btnStyle = window.getComputedStyle(btn);
                    log(`Button display: ${btnStyle.display}`, 'info');
                    log(`Button visibility: ${btnStyle.visibility}`, 'info');
                    log(`Button opacity: ${btnStyle.opacity}`, 'info');
                }
                
                if (statusText) {
                    log(`Status text: "${statusText.textContent.trim()}"`, 'info');
                }
                
                // Check app instance
                if (window.binanceApp) {
                    log('App instance found', 'success');
                    log(`Loading complete: ${window.binanceApp.loadingComplete}`, 'info');
                } else {
                    log('App instance NOT found', 'error');
                }
                
                log('UI test completed', 'success');
            }, 3000);
        }
        
        async function testClickSimulation() {
            clearResult();
            log('=== CLICK SIMULATION TEST ===', 'info');
            
            // Navigate to main page
            window.location.href = '/';
            
            setTimeout(() => {
                const btn = document.getElementById('start-analysis-btn');
                
                if (btn) {
                    log('Button found, testing click...', 'info');
                    
                    // Add event listener to detect click
                    btn.addEventListener('click', () => {
                        log('✅ Click event fired!', 'success');
                    });
                    
                    // Simulate click
                    btn.click();
                    
                    log('Click simulation completed', 'success');
                } else {
                    log('❌ Button not found for click test', 'error');
                }
            }, 3000);
        }
        
        function openMainPageTest() {
            clearResult();
            log('=== MAIN PAGE TEST ===', 'info');
            log('Opening main page...', 'info');
            
            // Open main page in current window
            window.location.href = '/?test=final';
        }
        
        async function runCompleteTest() {
            clearResult();
            log('🚀 STARTING COMPLETE MANUAL ANALYSIS TEST', 'info');
            log('==========================================', 'info');
            
            // Test 1: API Test
            log('1. Testing API endpoints...', 'info');
            try {
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();
                log(`✅ Status API: ${statusData.success ? 'OK' : 'FAIL'}`, statusData.success ? 'success' : 'error');
                
                const startResponse = await fetch('/api/start-analysis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const startData = await startResponse.json();
                log(`✅ Start Analysis API: ${startData.success ? 'OK' : 'FAIL'}`, startData.success ? 'success' : 'error');
                
                const progressResponse = await fetch('/api/analysis-progress');
                const progressData = await progressResponse.json();
                log(`✅ Progress API: ${progressData.success ? 'OK' : 'FAIL'}`, progressData.success ? 'success' : 'error');
                
            } catch (error) {
                log(`❌ API Test Failed: ${error.message}`, 'error');
            }
            
            log('2. Navigating to main page for UI test...', 'info');
            
            // Navigate to main page for UI test
            setTimeout(() => {
                window.location.href = '/?test=complete';
            }, 2000);
        }
        
        // Check if we're coming from a test
        const urlParams = new URLSearchParams(window.location.search);
        const testType = urlParams.get('test');
        
        if (testType === 'complete') {
            setTimeout(() => {
                log('3. Testing UI elements on main page...', 'info');
                testUIOnly();
            }, 1000);
        } else if (testType === 'final') {
            setTimeout(() => {
                log('Testing main page elements...', 'info');
                testUIOnly();
            }, 1000);
        }
        
        log('🎯 Final test page loaded', 'success');
        log('Click buttons above to run specific tests', 'info');
        
    </script>
</body>
</html>
