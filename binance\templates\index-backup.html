<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance Signal Generator Pro - AI-Powered Trading Signals</title>
    <meta name="description" content="Real-time technical analysis dan AI-powered trading signals untuk 500+ Binance futures pairs">
    <meta name="keywords" content="binance, trading signals, technical analysis, cryptocurrency, AI, gemini">
    <meta name="author" content="BOBACHEESE">
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🚀</div>
            <div class="loading-text">Binance Signal Generator Pro</div>
            <div class="loading-spinner"></div>
            <div class="loading-status">Memuat sistem analisis...</div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container" id="main-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="logo-icon">🚀</span>
                    <span class="logo-text">Binance Signal Generator Pro</span>
                </div>
                <div class="status-indicators">
                    <div class="status-indicator">
                        <div class="status-dot loading" id="binance-status"></div>
                        <span>Binance API</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot loading" id="ai-status"></div>
                        <span>Gemini AI</span>
                    </div>
                </div>
            </div>
            
            <div class="header-center">
                <div class="market-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Pairs</span>
                        <span class="stat-value" id="total-pairs">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Signals Generated</span>
                        <span class="stat-value" id="signals-count">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Analysis Time</span>
                        <span class="stat-value" id="analysis-time">-</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="current-time" id="current-time"></div>
                <button class="author-btn" id="author-btn">
                    <span class="author-icon">👨‍💻</span>
                    <span class="author-text">BOBACHEESE</span>
                </button>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="signals">
                <span class="tab-icon">📊</span>
                <span class="tab-text">Trading Signals</span>
            </button>
            <button class="nav-tab" data-tab="analysis">
                <span class="tab-icon">🔍</span>
                <span class="tab-text">Pair Analysis</span>
            </button>
            <button class="nav-tab" data-tab="charts">
                <span class="tab-icon">📈</span>
                <span class="tab-text">Live Charts</span>
            </button>
            <button class="nav-tab" data-tab="settings">
                <span class="tab-icon">⚙️</span>
                <span class="tab-text">Settings</span>
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Trading Signals Tab -->
            <div class="tab-content active" id="signals-tab">
                <!-- Analysis Control Panel -->
                <div class="analysis-control-panel" id="analysis-control-panel">
                    <div class="control-header">
                        <h3 class="control-title">🚀 Kontrol Analisis Manual</h3>
                        <div class="control-status" id="control-status">
                            <span class="status-text">Siap untuk analisis</span>
                            <div class="status-indicator idle" id="analysis-status-indicator"></div>
                        </div>
                    </div>

                    <div class="control-actions">
                        <button class="start-analysis-btn" id="start-analysis-btn">
                            <span class="btn-icon">▶️</span>
                            <span class="btn-text">Mulai Analisis</span>
                        </button>

                        <div class="analysis-info" id="analysis-info">
                            <div class="info-item">
                                <span class="info-label">Target Pairs:</span>
                                <span class="info-value">449 USDT pairs</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Estimasi Waktu:</span>
                                <span class="info-value">4-5 menit</span>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar (hidden by default) -->
                    <div class="progress-container" id="progress-container" style="display: none;">
                        <div class="progress-header">
                            <span class="progress-text" id="progress-text">Menganalisis...</span>
                            <span class="progress-percentage" id="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-details">
                            <span class="current-pair" id="current-pair">-</span>
                            <span class="estimated-time" id="estimated-time">-</span>
                        </div>
                    </div>
                </div>

                <div class="content-header">
                    <h2 class="content-title">🎯 Trading Signals Real-time</h2>
                    <div class="content-controls">
                        <div class="filter-group">
                            <label for="signal-filter">Filter Signal:</label>
                            <select id="signal-filter" class="filter-select">
                                <option value="all">Semua Signal</option>
                                <option value="STRONG_BUY">Strong Buy</option>
                                <option value="BUY">Buy</option>
                                <option value="HOLD">Hold</option>
                                <option value="SELL">Sell</option>
                                <option value="STRONG_SELL">Strong Sell</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="confidence-filter">Min Confidence:</label>
                            <input type="range" id="confidence-filter" min="0" max="100" value="50" class="filter-range">
                            <span id="confidence-value">50%</span>
                        </div>
                        <button class="refresh-btn" id="refresh-signals">
                            <span class="btn-icon">🔄</span>
                            <span class="btn-text">Refresh</span>
                        </button>
                    </div>
                </div>
                
                <div class="signals-container">
                    <div class="signals-grid" id="signals-grid">
                        <!-- Signals akan dimuat di sini -->
                    </div>
                </div>
            </div>

            <!-- Pair Analysis Tab -->
            <div class="tab-content" id="analysis-tab">
                <div class="content-header">
                    <h2 class="content-title">🔍 Analisis Pair Mendalam</h2>
                    <div class="content-controls">
                        <div class="search-group">
                            <input type="text" id="pair-search" placeholder="Cari pair (contoh: BTCUSDT)" class="search-input">
                            <button class="search-btn" id="search-pair">
                                <span class="btn-icon">🔍</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="analysis-container">
                    <div class="analysis-result" id="analysis-result">
                        <div class="empty-state">
                            <div class="empty-icon">📊</div>
                            <div class="empty-text">Masukkan symbol pair untuk memulai analisis</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Charts Tab -->
            <div class="tab-content" id="charts-tab">
                <div class="content-header">
                    <h2 class="content-title">📈 Live Charts</h2>
                    <div class="content-controls">
                        <div class="chart-controls">
                            <select id="chart-symbol" class="filter-select">
                                <option value="">Pilih Symbol</option>
                            </select>
                            <select id="chart-interval" class="filter-select">
                                <option value="1h">1 Hour</option>
                                <option value="4h">4 Hours</option>
                                <option value="1d">1 Day</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="price-chart"></canvas>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-content" id="settings-tab">
                <div class="content-header">
                    <h2 class="content-title">⚙️ Pengaturan Sistem</h2>
                </div>
                
                <div class="settings-container">
                    <div class="settings-section">
                        <h3 class="settings-title">🤖 Konfigurasi AI</h3>
                        <div class="setting-item">
                            <label for="gemini-api-key">Gemini API Key:</label>
                            <input type="password" id="gemini-api-key" placeholder="Masukkan API key Gemini" class="setting-input">
                            <button class="setting-btn" id="save-api-key">Simpan</button>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h3 class="settings-title">📊 Preferensi Analisis</h3>
                        <div class="setting-item">
                            <label for="auto-refresh">Auto Refresh:</label>
                            <input type="checkbox" id="auto-refresh" checked class="setting-checkbox">
                        </div>
                        <div class="setting-item">
                            <label for="refresh-interval">Interval Refresh (detik):</label>
                            <input type="number" id="refresh-interval" value="30" min="10" max="300" class="setting-input">
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Author Modal -->
    <div class="modal" id="author-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">👨‍💻 About Developer</h3>
                <button class="modal-close" id="close-author-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="author-info">
                    <div class="author-avatar">🚀</div>
                    <div class="author-details">
                        <h4 class="author-name">AMARULLOH ZIKRI</h4>
                        <p class="author-title">Senior Quantitative Trading Developer</p>
                        <div class="author-links">
                            <a href="https://github.com/bobacheese" target="_blank" class="author-link">
                                <span class="link-icon">🐙</span>
                                <span class="link-text">GitHub</span>
                            </a>
                            <a href="https://youtube.com/@bobacheese?si=5M2leEilS3_VmNS6" target="_blank" class="author-link">
                                <span class="link-icon">📺</span>
                                <span class="link-text">YouTube</span>
                            </a>
                            <a href="https://coff.ee/amarullohzd" target="_blank" class="author-link">
                                <span class="link-icon">☕</span>
                                <span class="link-text">Buy Coffee</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notification-container"></div>

    <!-- Force Fix Script for Manual Analysis -->
    <script>
        console.log('[FORCE] Force fix script loaded in template');

        // Force fix loading screen issue
        function forceFixLoadingScreen() {
            console.log('[FORCE] Starting force fix...');

            const loadingScreen = document.getElementById('loading-screen');
            const mainContainer = document.getElementById('main-container');

            if (loadingScreen) {
                console.log('[FORCE] Forcing loading screen to hide...');
                loadingScreen.style.display = 'none';
                loadingScreen.style.opacity = '0';
                loadingScreen.style.visibility = 'hidden';
            }

            if (mainContainer) {
                console.log('[FORCE] Forcing main container to show...');
                mainContainer.style.display = 'flex';
                mainContainer.style.opacity = '1';
                mainContainer.style.visibility = 'visible';
            }

            // Force initialize manual analysis UI
            setTimeout(() => {
                forceInitializeManualAnalysisUI();
            }, 500);
        }

        function forceInitializeManualAnalysisUI() {
            console.log('[FORCE] Force initializing manual analysis UI...');

            const btn = document.getElementById('start-analysis-btn');
            const statusText = document.querySelector('.status-text');
            const statusIndicator = document.getElementById('analysis-status-indicator');

            if (btn && statusText && statusIndicator) {
                console.log('[FORCE] All elements found, updating UI...');

                // Reset to idle state
                btn.className = 'start-analysis-btn';
                btn.innerHTML = '<span class="btn-icon">▶️</span><span class="btn-text">Mulai Analisis</span>';
                btn.disabled = false;

                statusText.textContent = 'Siap untuk analisis';

                statusIndicator.className = 'status-indicator idle';

                // Add click handler
                btn.addEventListener('click', async () => {
                    console.log('[FORCE] Manual analysis button clicked');

                    try {
                        // Update UI to running state
                        btn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">Sedang Analisis...</span>';
                        btn.disabled = true;
                        btn.classList.add('analyzing');
                        statusText.textContent = 'Analisis sedang berjalan';
                        statusIndicator.className = 'status-indicator running';

                        // Call API
                        const response = await fetch('/api/start-analysis', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        const data = await response.json();

                        if (data.success) {
                            console.log('[FORCE] Analysis started successfully');
                            alert('✅ Analisis dimulai! ' + data.message);

                            // Start monitoring progress
                            monitorProgress();
                        } else {
                            throw new Error(data.error || 'Failed to start analysis');
                        }

                    } catch (error) {
                        console.error('[FORCE] Error starting analysis:', error);
                        alert('❌ Error: ' + error.message);

                        // Reset to idle state
                        btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Coba Lagi</span>';
                        btn.disabled = false;
                        statusText.textContent = 'Analisis gagal';
                        statusIndicator.className = 'status-indicator error';
                    }
                });

                console.log('[FORCE] Manual analysis UI initialized successfully!');

                // Show success notification
                setTimeout(() => {
                    alert('🎉 Manual Analysis UI berhasil diinisialisasi!\n\nTombol "Mulai Analisis" sekarang dapat diklik.');
                }, 1000);

            } else {
                console.error('[FORCE] Required elements not found:', {
                    btn: !!btn,
                    statusText: !!statusText,
                    statusIndicator: !!statusIndicator
                });

                // Retry after delay
                setTimeout(forceInitializeManualAnalysisUI, 1000);
            }
        }

        function monitorProgress() {
            const progressInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/analysis-progress');
                    const data = await response.json();

                    if (data.success && !data.progress.is_running) {
                        clearInterval(progressInterval);

                        const btn = document.getElementById('start-analysis-btn');
                        const statusText = document.querySelector('.status-text');
                        const statusIndicator = document.getElementById('analysis-status-indicator');

                        if (data.progress.error_message) {
                            btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Coba Lagi</span>';
                            btn.disabled = false;
                            statusText.textContent = 'Analisis gagal';
                            statusIndicator.className = 'status-indicator error';
                            alert('❌ Analisis gagal: ' + data.progress.error_message);
                        } else {
                            btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Analisis Ulang</span>';
                            btn.disabled = false;
                            btn.classList.add('completed');
                            statusText.textContent = 'Analisis selesai';
                            statusIndicator.className = 'status-indicator completed';
                            alert('✅ Analisis selesai! Signals telah diperbarui.');
                        }
                    }
                } catch (error) {
                    console.error('[FORCE] Error monitoring progress:', error);
                }
            }, 2000);
        }

        // Execute force fix immediately and with delays
        setTimeout(forceFixLoadingScreen, 100);
        setTimeout(forceFixLoadingScreen, 1000);
        setTimeout(forceFixLoadingScreen, 3000);
        setTimeout(forceFixLoadingScreen, 5000);

        // Also execute when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', forceFixLoadingScreen);
        } else {
            forceFixLoadingScreen();
        }

    </script>

    <!-- Custom JavaScript -->
    <script src="/static/js/app.js"></script>
</body>
</html>
