<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Manual Analysis Test</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 2px 0; font-size: 12px; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
        .test-section { border: 1px solid #333; margin: 10px 0; padding: 10px; }
        .test-button { margin: 5px; padding: 8px 16px; background: #333; color: #0f0; border: 1px solid #0f0; cursor: pointer; }
        .test-button:hover { background: #0f0; color: #000; }
    </style>
</head>
<body>
    <h1>🔍 Comprehensive Manual Analysis Test</h1>
    
    <div class="test-section">
        <h3>🎮 Test Controls</h3>
        <button class="test-button" onclick="runFullTest()">🚀 Run Full Test</button>
        <button class="test-button" onclick="testElementsOnly()">🔍 Test Elements Only</button>
        <button class="test-button" onclick="testAPIOnly()">🌐 Test API Only</button>
        <button class="test-button" onclick="testClickEvent()">👆 Test Click Event</button>
        <button class="test-button" onclick="clearLog()">🗑️ Clear Log</button>
    </div>
    
    <div class="test-section">
        <h3>📊 Test Results</h3>
        <div id="console-output"></div>
    </div>
    
    <script>
        const output = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
            console.log(`[TEST] ${message}`);
        }
        
        function clearLog() {
            output.innerHTML = '';
        }
        
        async function testElementsOnly() {
            log('=== TESTING ELEMENTS ===', 'info');
            
            // Test if we're on the right page
            if (window.location.pathname !== '/') {
                log('❌ Not on main page, redirecting...', 'error');
                window.location.href = '/';
                return;
            }
            
            // Test loading screen state
            const loadingScreen = document.getElementById('loading-screen');
            const mainContainer = document.getElementById('main-container');
            
            if (loadingScreen) {
                const loadingDisplay = window.getComputedStyle(loadingScreen).display;
                log(`Loading screen display: ${loadingDisplay}`, loadingDisplay === 'none' ? 'success' : 'error');
            } else {
                log('❌ Loading screen element not found', 'error');
            }
            
            if (mainContainer) {
                const mainDisplay = window.getComputedStyle(mainContainer).display;
                log(`Main container display: ${mainDisplay}`, mainDisplay !== 'none' ? 'success' : 'error');
            } else {
                log('❌ Main container element not found', 'error');
            }
            
            // Test manual analysis elements
            const elements = {
                'start-analysis-btn': document.getElementById('start-analysis-btn'),
                'analysis-status-indicator': document.getElementById('analysis-status-indicator'),
                'progress-container': document.getElementById('progress-container'),
                'progress-text': document.getElementById('progress-text'),
                'progress-percentage': document.getElementById('progress-percentage'),
                'progress-fill': document.getElementById('progress-fill'),
                'current-pair': document.getElementById('current-pair'),
                'estimated-time': document.getElementById('estimated-time')
            };
            
            const statusText = document.querySelector('.status-text');
            if (statusText) {
                elements['status-text'] = statusText;
            }
            
            let allElementsFound = true;
            
            for (const [name, element] of Object.entries(elements)) {
                if (element) {
                    log(`✅ ${name}: Found`, 'success');
                    
                    // Test visibility
                    const display = window.getComputedStyle(element).display;
                    const visibility = window.getComputedStyle(element).visibility;
                    const opacity = window.getComputedStyle(element).opacity;
                    
                    log(`   Display: ${display}, Visibility: ${visibility}, Opacity: ${opacity}`, 'info');
                    
                    if (name === 'start-analysis-btn') {
                        log(`   Button text: "${element.textContent}"`, 'info');
                        log(`   Button disabled: ${element.disabled}`, 'info');
                        log(`   Button className: "${element.className}"`, 'info');
                    }
                    
                    if (name === 'status-text') {
                        log(`   Status text: "${element.textContent}"`, 'info');
                    }
                    
                } else {
                    log(`❌ ${name}: NOT FOUND`, 'error');
                    allElementsFound = false;
                }
            }
            
            log(`Elements test result: ${allElementsFound ? 'PASS' : 'FAIL'}`, allElementsFound ? 'success' : 'error');
            return allElementsFound;
        }
        
        async function testAPIOnly() {
            log('=== TESTING API ===', 'info');
            
            try {
                // Test status API
                log('Testing /api/status...', 'info');
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();
                
                if (statusData.success) {
                    log('✅ Status API: Working', 'success');
                    log(`   Analysis status: ${JSON.stringify(statusData.analysis_status.is_running)}`, 'info');
                } else {
                    log('❌ Status API: Failed', 'error');
                    return false;
                }
                
                // Test start analysis API
                log('Testing /api/start-analysis...', 'info');
                const startResponse = await fetch('/api/start-analysis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const startData = await startResponse.json();
                
                if (startData.success) {
                    log('✅ Start Analysis API: Working', 'success');
                    log(`   Message: ${startData.message}`, 'info');
                } else {
                    log('❌ Start Analysis API: Failed', 'error');
                    log(`   Error: ${startData.error}`, 'error');
                }
                
                // Test progress API
                log('Testing /api/analysis-progress...', 'info');
                const progressResponse = await fetch('/api/analysis-progress');
                const progressData = await progressResponse.json();
                
                if (progressData.success) {
                    log('✅ Progress API: Working', 'success');
                    log(`   Progress: ${progressData.progress.progress}/${progressData.progress.total_pairs}`, 'info');
                } else {
                    log('❌ Progress API: Failed', 'error');
                }
                
                log('API test result: PASS', 'success');
                return true;
                
            } catch (error) {
                log(`❌ API test failed: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testClickEvent() {
            log('=== TESTING CLICK EVENT ===', 'info');
            
            const btn = document.getElementById('start-analysis-btn');
            if (!btn) {
                log('❌ Button not found for click test', 'error');
                return false;
            }
            
            log('Button found, testing click...', 'info');
            
            // Add temporary event listener to detect click
            let clickDetected = false;
            const tempListener = () => {
                clickDetected = true;
                log('✅ Click event detected!', 'success');
            };
            
            btn.addEventListener('click', tempListener);
            
            // Simulate click
            btn.click();
            
            // Wait a bit and check
            setTimeout(() => {
                btn.removeEventListener('click', tempListener);
                if (clickDetected) {
                    log('Click test result: PASS', 'success');
                } else {
                    log('❌ Click test result: FAIL - No click detected', 'error');
                }
            }, 1000);
            
            return true;
        }
        
        async function runFullTest() {
            log('🚀 STARTING COMPREHENSIVE TEST', 'info');
            log('================================', 'info');
            
            const elementsOK = await testElementsOnly();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const apiOK = await testAPIOnly();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testClickEvent();
            
            log('================================', 'info');
            log(`🏁 FINAL RESULT: ${elementsOK && apiOK ? 'PASS' : 'FAIL'}`, elementsOK && apiOK ? 'success' : 'error');
        }
        
        // Auto-run test when page loads
        window.addEventListener('load', () => {
            log('🔄 Page loaded, running initial test...', 'info');
            setTimeout(runFullTest, 2000);
        });
        
    </script>
</body>
</html>
