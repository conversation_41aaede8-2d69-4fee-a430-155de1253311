#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance Signal Generator Pro - Test Suite
Comprehensive testing untuk memastikan functionality dan performance

Author: BOBACHEESE
Version: 1.0.0
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules to test
from main import (
    BinanceAPIClient, TechnicalAnalysisEngine, AISignalGenerator,
    SignalEngine, RateLimiter, TradingPair, CandlestickData,
    TechnicalIndicators, TradingSignal
)

class TestRateLimiter(unittest.TestCase):
    """Test rate limiting functionality"""
    
    def setUp(self):
        self.rate_limiter = RateLimiter(max_calls=5, time_window=1)
    
    def test_rate_limit_allows_calls_within_limit(self):
        """Test that calls within limit are allowed"""
        for i in range(5):
            self.assertTrue(self.rate_limiter.can_make_call())
            self.rate_limiter.record_call()
    
    def test_rate_limit_blocks_calls_over_limit(self):
        """Test that calls over limit are blocked"""
        # Use up all allowed calls
        for i in range(5):
            self.rate_limiter.record_call()
        
        # Next call should be blocked
        self.assertFalse(self.rate_limiter.can_make_call())
    
    def test_rate_limit_resets_after_time_window(self):
        """Test that rate limit resets after time window"""
        # Use up all calls
        for i in range(5):
            self.rate_limiter.record_call()
        
        # Should be blocked
        self.assertFalse(self.rate_limiter.can_make_call())
        
        # Wait for time window to pass
        time.sleep(1.1)
        
        # Should be allowed again
        self.assertTrue(self.rate_limiter.can_make_call())

class TestBinanceAPIClient(unittest.TestCase):
    """Test Binance API client functionality"""
    
    def setUp(self):
        self.client = BinanceAPIClient()
    
    @patch('requests.Session.get')
    def test_successful_api_request(self, mock_get):
        """Test successful API request"""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'test': 'data'}
        mock_get.return_value = mock_response
        
        result = self.client._make_request('/test')
        
        self.assertEqual(result, {'test': 'data'})
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_rate_limit_handling(self, mock_get):
        """Test rate limit response handling"""
        # Mock rate limit response
        mock_response = Mock()
        mock_response.status_code = 429
        mock_get.return_value = mock_response
        
        with patch('time.sleep') as mock_sleep:
            result = self.client._make_request('/test')
            mock_sleep.assert_called_with(60)
    
    @patch('requests.Session.get')
    def test_error_handling(self, mock_get):
        """Test error response handling"""
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = 'Internal Server Error'
        mock_get.return_value = mock_response
        
        result = self.client._make_request('/test')
        
        self.assertIsNone(result)
    
    def test_cache_functionality(self):
        """Test caching mechanism"""
        # Set test data in cache
        test_data = {'test': 'cached_data'}
        self.client._set_cached_data('test_key', test_data)
        
        # Retrieve from cache
        cached_data = self.client._get_cached_data('test_key')
        
        self.assertEqual(cached_data, test_data)
    
    def test_cache_expiry(self):
        """Test cache expiry functionality"""
        # Set test data in cache
        test_data = {'test': 'cached_data'}
        self.client._set_cached_data('test_key', test_data)
        
        # Manually expire cache
        self.client.cache_timestamps['test_key'] = time.time() - 1000
        
        # Should return None for expired cache
        cached_data = self.client._get_cached_data('test_key')
        self.assertIsNone(cached_data)

class TestTechnicalAnalysisEngine(unittest.TestCase):
    """Test technical analysis functionality"""
    
    def setUp(self):
        self.ta_engine = TechnicalAnalysisEngine()
        
        # Create sample candlestick data
        self.sample_candlesticks = []
        base_price = 50000
        for i in range(250):  # Need enough data for indicators
            candle = CandlestickData(
                open_time=time.time() - (250-i) * 3600,
                open_price=base_price + i * 10,
                high_price=base_price + i * 10 + 100,
                low_price=base_price + i * 10 - 100,
                close_price=base_price + i * 10 + 50,
                volume=1000000,
                close_time=time.time() - (250-i) * 3600 + 3600,
                quote_volume=50000000000,
                trades_count=1000
            )
            self.sample_candlesticks.append(candle)
    
    def test_indicator_calculation(self):
        """Test technical indicator calculations"""
        indicators = self.ta_engine.calculate_indicators(self.sample_candlesticks)
        
        # Check that indicators are calculated
        self.assertIsInstance(indicators, TechnicalIndicators)
        self.assertGreater(indicators.sma_21, 0)
        self.assertGreater(indicators.ema_21, 0)
        self.assertIsNotNone(indicators.rsi)
        self.assertIsNotNone(indicators.macd_line)
    
    def test_insufficient_data_handling(self):
        """Test handling of insufficient data"""
        # Use only 10 candlesticks (insufficient for most indicators)
        short_data = self.sample_candlesticks[:10]
        
        indicators = self.ta_engine.calculate_indicators(short_data)
        
        # Should return default TechnicalIndicators object
        self.assertIsInstance(indicators, TechnicalIndicators)
    
    def test_support_resistance_calculation(self):
        """Test support and resistance level calculation"""
        import pandas as pd
        
        # Create test DataFrame
        df = pd.DataFrame([{
            'high': c.high_price,
            'low': c.low_price,
            'close': c.close_price
        } for c in self.sample_candlesticks])
        
        support, resistance = self.ta_engine._calculate_support_resistance(df)
        
        self.assertIsInstance(support, float)
        self.assertIsInstance(resistance, float)
        self.assertLess(support, resistance)
    
    def test_pattern_detection(self):
        """Test pattern detection functionality"""
        import pandas as pd
        
        # Create test DataFrame
        df = pd.DataFrame([{
            'close': c.close_price,
            'volume': c.volume
        } for c in self.sample_candlesticks])
        
        patterns, confidence = self.ta_engine._detect_patterns(df)
        
        self.assertIsInstance(patterns, list)
        self.assertIsInstance(confidence, float)
        self.assertGreaterEqual(confidence, 0)
        self.assertLessEqual(confidence, 100)

class TestAISignalGenerator(unittest.TestCase):
    """Test AI signal generation functionality"""
    
    def setUp(self):
        self.ai_generator = AISignalGenerator()
        
        # Create sample trading pair
        self.sample_pair = TradingPair(
            symbol='BTCUSDT',
            base_asset='BTC',
            quote_asset='USDT',
            price=50000.0,
            volume_24h=1000000000,
            change_24h=2.5,
            high_24h=51000.0,
            low_24h=49000.0
        )
        
        # Create sample indicators
        self.sample_indicators = TechnicalIndicators(
            sma_21=49500.0,
            ema_21=49800.0,
            rsi=65.0,
            macd_line=100.0,
            macd_signal=80.0,
            bb_upper=51000.0,
            bb_lower=48000.0,
            support_level=48500.0,
            resistance_level=51500.0
        )
    
    def test_technical_score_calculation(self):
        """Test technical score calculation"""
        score = self.ai_generator._calculate_technical_score(
            self.sample_indicators, 
            self.sample_pair.price
        )
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)
    
    def test_signal_type_determination(self):
        """Test signal type determination"""
        # Test different score ranges
        self.assertEqual(self.ai_generator._determine_signal_type(85), "STRONG_BUY")
        self.assertEqual(self.ai_generator._determine_signal_type(70), "BUY")
        self.assertEqual(self.ai_generator._determine_signal_type(50), "HOLD")
        self.assertEqual(self.ai_generator._determine_signal_type(25), "SELL")
        self.assertEqual(self.ai_generator._determine_signal_type(15), "STRONG_SELL")
    
    def test_entry_points_calculation(self):
        """Test entry points calculation"""
        entry_points = self.ai_generator._calculate_entry_points(
            self.sample_pair.price,
            self.sample_indicators
        )
        
        self.assertIsInstance(entry_points, list)
        self.assertLessEqual(len(entry_points), 3)
        self.assertIn(self.sample_pair.price, entry_points)
    
    def test_stop_loss_calculation(self):
        """Test stop loss calculation"""
        stop_loss = self.ai_generator._calculate_stop_loss(
            self.sample_pair.price,
            self.sample_indicators,
            "BUY"
        )
        
        self.assertIsInstance(stop_loss, float)
        self.assertLess(stop_loss, self.sample_pair.price)  # Stop loss should be below current price for buy signal
    
    def test_take_profit_calculation(self):
        """Test take profit calculation"""
        take_profits = self.ai_generator._calculate_take_profit(
            self.sample_pair.price,
            self.sample_indicators,
            "BUY"
        )
        
        self.assertIsInstance(take_profits, list)
        self.assertLessEqual(len(take_profits), 3)
        for tp in take_profits:
            self.assertGreater(tp, self.sample_pair.price)  # Take profits should be above current price for buy signal
    
    def test_risk_level_calculation(self):
        """Test risk level calculation"""
        risk_level = self.ai_generator._calculate_risk_level(
            self.sample_indicators,
            self.sample_pair.change_24h
        )
        
        self.assertIn(risk_level, ["LOW", "MEDIUM", "HIGH"])

class TestSignalEngine(unittest.TestCase):
    """Test main signal engine functionality"""
    
    def setUp(self):
        self.signal_engine = SignalEngine()
    
    @patch.object(BinanceAPIClient, 'get_candlestick_data')
    @patch.object(TechnicalAnalysisEngine, 'calculate_indicators')
    @patch.object(AISignalGenerator, 'generate_signal')
    def test_analyze_pair(self, mock_generate_signal, mock_calculate_indicators, mock_get_candlestick_data):
        """Test single pair analysis"""
        # Mock data
        mock_candlesticks = [Mock() for _ in range(250)]
        mock_get_candlestick_data.return_value = mock_candlesticks
        
        mock_indicators = TechnicalIndicators()
        mock_calculate_indicators.return_value = mock_indicators
        
        mock_signal = TradingSignal(
            symbol='BTCUSDT',
            signal_type='BUY',
            confidence=75.0,
            entry_points=[50000.0],
            stop_loss=48000.0,
            take_profit=[52000.0],
            timeframe='1H',
            reasoning='Test signal',
            risk_level='MEDIUM',
            ai_analysis='Test AI analysis',
            technical_score=75.0,
            timestamp=time.time()
        )
        mock_generate_signal.return_value = mock_signal
        
        # Test pair
        test_pair = TradingPair(
            symbol='BTCUSDT',
            base_asset='BTC',
            quote_asset='USDT',
            price=50000.0,
            volume_24h=1000000000,
            change_24h=2.5,
            high_24h=51000.0,
            low_24h=49000.0
        )
        
        result = self.signal_engine.analyze_pair(test_pair)
        
        self.assertIsInstance(result, TradingSignal)
        self.assertEqual(result.symbol, 'BTCUSDT')
    
    def test_stats_tracking(self):
        """Test statistics tracking"""
        initial_stats = self.signal_engine.stats.copy()
        
        # Stats should be initialized
        self.assertIn('total_analyzed', initial_stats)
        self.assertIn('signals_generated', initial_stats)
        self.assertIn('analysis_time', initial_stats)

class TestPerformance(unittest.TestCase):
    """Test performance requirements"""
    
    def setUp(self):
        self.signal_engine = SignalEngine()
    
    @patch.object(BinanceAPIClient, 'get_top_pairs')
    def test_memory_usage(self, mock_get_top_pairs):
        """Test memory usage stays within limits"""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Mock large dataset
        mock_pairs = [
            TradingPair(
                symbol=f'PAIR{i}USDT',
                base_asset=f'PAIR{i}',
                quote_asset='USDT',
                price=100.0,
                volume_24h=1000000,
                change_24h=1.0,
                high_24h=101.0,
                low_24h=99.0
            ) for i in range(100)
        ]
        mock_get_top_pairs.return_value = mock_pairs
        
        # Process data
        pairs = self.signal_engine.binance_client.get_top_pairs(100)
        
        # Check memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Should not increase memory by more than 500MB for this test
        self.assertLess(memory_increase, 500)

if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestRateLimiter,
        TestBinanceAPIClient,
        TestTechnicalAnalysisEngine,
        TestAISignalGenerator,
        TestSignalEngine,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
    
    # Exit with appropriate code
    exit_code = 0 if result.wasSuccessful() else 1
    sys.exit(exit_code)
