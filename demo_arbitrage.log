2025-07-13 02:48:24,534 - [DEMO] - INFO - [DEMO] Starting CryptoArb Pro in Demo Mode...
2025-07-13 02:48:25,857 - [DEMO] - INFO - [DEMO] Performing initial demo scan...
2025-07-13 02:48:25,857 - [DEMO] - INFO - [DEMO] Scanning demo opportunities...
2025-07-13 02:48:25,858 - [DEMO] - INFO - [DEMO] Found 4 demo opportunities
2025-07-13 02:48:25,858 - [DEMO] - INFO - [DEMO] Initial scan found 4 demo opportunities
2025-07-13 02:48:25,858 - [DEMO] - INFO - [DEMO] Starting demo web interface...
2025-07-13 02:48:25,859 - [DEMO] - INFO - [DEMO] Access the application at: http://localhost:5000
2025-07-13 02:48:25,859 - [DEMO] - INFO - [DEMO] Use Ctrl+C to stop the demo application
2025-07-13 02:48:25,882 - [DEMO] - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:48:25,882 - [DEMO] - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:48:59,912 - [DEMO] - INFO - [DEMO] Starting CryptoArb Pro in Demo Mode...
2025-07-13 02:49:01,367 - [DEMO] - INFO - [DEMO] Performing initial demo scan...
2025-07-13 02:49:01,367 - [DEMO] - INFO - [DEMO] Scanning demo opportunities...
2025-07-13 02:49:01,367 - [DEMO] - INFO - [DEMO] Found 5 demo opportunities
2025-07-13 02:49:01,368 - [DEMO] - INFO - [DEMO] Initial scan found 5 demo opportunities
2025-07-13 02:49:01,368 - [DEMO] - INFO - [DEMO] Starting demo web interface...
2025-07-13 02:49:01,368 - [DEMO] - INFO - [DEMO] Access the application at: http://localhost:5000
2025-07-13 02:49:01,369 - [DEMO] - INFO - [DEMO] Use Ctrl+C to stop the demo application
2025-07-13 02:49:01,397 - [DEMO] - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:49:01,397 - [DEMO] - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:51:22,052 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:22] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:51:24,187 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:24] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:24,497 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:24] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:32,361 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:32] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:42,882 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:52,365 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:52] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:52,367 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:52] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:52:02,902 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:52:02] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:52:22,351 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:52:22] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:52:41,895 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:52:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:53:41,378 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:53:41] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:53:42,211 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:53:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:54:08,410 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:08] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:54:08,853 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:54:31,352 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:31] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:54:41,367 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:41] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:54:41,904 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:54:42,266 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:54:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:55:01,391 - [DEMO] - INFO - 127.0.0.1 - - [13/Jul/2025 02:55:01] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:55:06,682 - [DEMO] - INFO - [DEMO] Demo cleanup completed
