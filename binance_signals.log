2025-07-13 11:19:01,229 - __main__ - INFO - [BINANCE] API Client initialized
2025-07-13 11:19:01,230 - __main__ - INFO - [TA] Technical Analysis Engine initialized
2025-07-13 11:19:01,230 - __main__ - WARNING - [AI] No Gemini API key provided, AI features disabled
2025-07-13 11:19:01,231 - __main__ - INFO - [ENGINE] Signal Engine initialized
2025-07-13 11:19:01,241 - __main__ - INFO - [OK] All dependencies available
2025-07-13 11:19:03,143 - __main__ - INFO - [OK] Binance API accessible
2025-07-13 11:19:03,144 - __main__ - WARNING - [WARNING] Gemini API key not configured - AI features disabled
2025-07-13 11:19:03,144 - __main__ - INFO - [START] Starting Binance Signal Generator Pro...
2025-07-13 11:19:03,145 - __main__ - INFO - [BACKGROUND] Background signal updater disabled - using manual control
2025-07-13 11:19:03,146 - __main__ - INFO - [BACKGROUND] Background updater started (manual control mode)
2025-07-13 11:19:03,146 - __main__ - INFO - [INIT] Skipping automatic signal generation - waiting for manual trigger
2025-07-13 11:19:03,146 - __main__ - INFO - [INIT] Use the 'Mulai Analisis' button in the web interface to start analysis
2025-07-13 11:19:03,147 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 11:19:03,147 - __main__ - INFO - [INFO] Access the application at: http://localhost:5001
2025-07-13 11:19:03,147 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 11:19:03,148 - __main__ - INFO - [INFO] Status: Siap untuk analisis manual
2025-07-13 11:19:03,168 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://********:5001
2025-07-13 11:19:03,169 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
