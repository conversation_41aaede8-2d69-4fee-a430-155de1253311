<!DOCTYPE html>
<html>
<head>
    <title>App Instance Test</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 2px 0; font-size: 12px; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
    </style>
</head>
<body>
    <h1>🔍 App Instance Test</h1>
    <div id="console-output"></div>
    
    <script>
        const output = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
            console.log(`[TEST] ${message}`);
        }
        
        function checkAppInstance() {
            log('=== CHECKING APP INSTANCE ===', 'info');
            
            // Check if window.binanceApp exists
            if (window.binanceApp) {
                log('✅ window.binanceApp exists', 'success');
                log(`   Type: ${typeof window.binanceApp}`, 'info');
                log(`   Constructor: ${window.binanceApp.constructor.name}`, 'info');
                
                // Check properties
                const props = ['loadingComplete', 'signals', 'pairs', 'currentChart', 'refreshInterval', 'progressInterval'];
                props.forEach(prop => {
                    if (window.binanceApp.hasOwnProperty(prop)) {
                        log(`   ${prop}: ${window.binanceApp[prop]}`, 'info');
                    } else {
                        log(`   ${prop}: NOT FOUND`, 'warn');
                    }
                });
                
                // Check methods
                const methods = ['startManualAnalysis', 'updateAnalysisUI', 'startProgressMonitoring'];
                methods.forEach(method => {
                    if (typeof window.binanceApp[method] === 'function') {
                        log(`   ${method}(): Available`, 'success');
                    } else {
                        log(`   ${method}(): NOT AVAILABLE`, 'error');
                    }
                });
                
            } else {
                log('❌ window.binanceApp does NOT exist', 'error');
            }
            
            // Check if we can access main page elements
            log('=== CHECKING MAIN PAGE ACCESS ===', 'info');
            
            // Try to access via window.parent (if in iframe)
            try {
                const parentBtn = window.parent.document.getElementById('start-analysis-btn');
                if (parentBtn) {
                    log('✅ Found button via window.parent', 'success');
                } else {
                    log('❌ Button not found via window.parent', 'error');
                }
            } catch (e) {
                log(`❌ Cannot access window.parent: ${e.message}`, 'error');
            }
            
            // Try to access via window.opener (if popup)
            try {
                const openerBtn = window.opener?.document.getElementById('start-analysis-btn');
                if (openerBtn) {
                    log('✅ Found button via window.opener', 'success');
                } else {
                    log('❌ Button not found via window.opener', 'error');
                }
            } catch (e) {
                log(`❌ Cannot access window.opener: ${e.message}`, 'error');
            }
        }
        
        function testDirectNavigation() {
            log('=== TESTING DIRECT NAVIGATION ===', 'info');
            log('Navigating to main page in 3 seconds...', 'warn');
            
            setTimeout(() => {
                window.location.href = '/?test=1';
            }, 3000);
        }
        
        // Run tests
        log('🚀 Starting app instance test...', 'info');
        checkAppInstance();
        
        // Check periodically
        let checkCount = 0;
        const interval = setInterval(() => {
            checkCount++;
            log(`--- Periodic check #${checkCount} ---`, 'info');
            checkAppInstance();
            
            if (checkCount >= 5) {
                clearInterval(interval);
                log('=== TEST COMPLETED ===', 'success');
                
                // Add navigation test button
                const navBtn = document.createElement('button');
                navBtn.textContent = '🔄 Test Direct Navigation';
                navBtn.onclick = testDirectNavigation;
                navBtn.style.cssText = 'margin: 10px; padding: 10px; background: #333; color: #0f0; border: 1px solid #0f0; cursor: pointer;';
                document.body.appendChild(navBtn);
            }
        }, 2000);
        
    </script>
</body>
</html>
