<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
</head>
<body>
    <h1>Simple JavaScript Test</h1>
    <div id="test-output"></div>
    
    <script>
        console.log('Simple test script loaded');
        
        // Test basic DOM manipulation
        document.getElementById('test-output').innerHTML = 'JavaScript is working!';
        
        // Test if we can access the main page elements
        setTimeout(() => {
            // Try to access main page via iframe or direct check
            const btn = parent.document?.getElementById('start-analysis-btn') || 
                       window.opener?.document?.getElementById('start-analysis-btn');
            
            if (btn) {
                console.log('Found button in parent/opener:', btn);
                document.getElementById('test-output').innerHTML += '<br>Button found in main page!';
            } else {
                console.log('Button not found in parent/opener');
                document.getElementById('test-output').innerHTML += '<br>Button NOT found in main page';
            }
            
            // Try direct navigation test
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Go to Main Page and Test';
            testBtn.onclick = () => {
                window.location.href = '/';
                setTimeout(() => {
                    const mainBtn = document.getElementById('start-analysis-btn');
                    if (mainBtn) {
                        console.log('Button found on main page!');
                        alert('Button found! Clicking it...');
                        mainBtn.click();
                    } else {
                        console.log('Button NOT found on main page');
                        alert('Button NOT found on main page');
                    }
                }, 2000);
            };
            document.body.appendChild(testBtn);
            
        }, 1000);
    </script>
</body>
</html>
