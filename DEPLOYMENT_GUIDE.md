# 🚀 CryptoArb Pro - Panduan Deployment

## 📋 Daftar File

### File Utama
- `main.py` - Program utama dengan semua fungsi arbitrase dan web interface
- `requirements.txt` - Dependencies Python yang diperlukan
- `config.py` - Konfigurasi sistem dan parameter trading
- `demo_data.py` - Data simulasi untuk mode demo
- `run_demo.py` - Script untuk menjalankan mode demo
- `start_cryptoarb.bat` - Script Windows untuk memudahkan startup

### File Dokumentasi
- `README.md` - Dokumentasi lengkap program
- `DEPLOYMENT_GUIDE.md` - Panduan deployment ini

## 🛠️ Instalasi Cepat

### 1. Persiapan Environment
```bash
# Pastikan Python 3.8+ terinstall
python --version

# Clone atau download project ke direktori
cd "C:\Users\<USER>\project 4"

# Install dependencies
pip install -r requirements.txt
```

### 2. Menjalankan Program

#### Opsi A: Menggunakan Script Batch (Recommended)
```bash
# Double-click atau jalankan dari command prompt
start_cryptoarb.bat
```

#### Opsi B: Manual
```bash
# Mode Normal (API Real)
python main.py

# Mode Demo (Data Simulasi)
python run_demo.py
```

## 🌐 Akses Web Interface

Setelah program berjalan, buka browser dan akses:
- **URL**: http://localhost:5000
- **Interface**: Dark futuristic theme dengan glassmorphism
- **Fitur**: Real-time arbitrage detection, filtering, analytics

## 🎯 Mode Operasi

### Mode Normal
- Menggunakan API real Binance dan Bybit
- Data harga real-time setiap 30 detik
- Memerlukan koneksi internet stabil
- Dapat terkena rate limiting atau geo-blocking

### Mode Demo
- Data simulasi untuk testing dan pembelajaran
- 5 peluang arbitrase demo dengan variasi harga
- Tidak memerlukan API access
- Ideal untuk testing interface dan fitur

## 🔧 Konfigurasi

### API Configuration (config.py)
```python
API_CONFIG = {
    'binance': {
        'base_url': 'https://api.binance.com/api/v3',
        'rate_limit': 1200,  # requests per minute
        'timeout': 10
    },
    'bybit': {
        'base_url': 'https://api.bybit.com/v5',
        'rate_limit': 120,   # requests per minute
        'timeout': 10
    }
}
```

### Trading Parameters
```python
TRADING_CONFIG = {
    'min_profit_percentage': 0.5,
    'max_profit_percentage': 200.0,
    'min_volume_24h': 10000,  # USD
    'trading_fee_percentage': 0.1,  # 0.1% per transaction
    'scan_interval': 30  # seconds
}
```

## 📊 Fitur Interface

### Dashboard Utama
- **Header**: Status koneksi API real-time
- **Sidebar**: Filter profit, volume, network
- **Main Table**: Daftar peluang arbitrase
- **Footer**: Statistics dan performance metrics

### Filter System
- **Profit Range**: 0.5% - 200% (slider)
- **Volume Minimum**: $1,000 - $1,000,000
- **Network Selection**: ETH, BSC, TRC20, Polygon, Arbitrum
- **Auto Refresh**: Toggle on/off

### Detail Modal
- **Price Information**: Harga di kedua bursa
- **Volume Analysis**: Volume 24 jam dan likuiditas
- **Network Options**: Jaringan transfer tersedia
- **Profit Calculator**: Estimasi profit berdasarkan investasi
- **Direct Links**: Link ke halaman trading

## 🚨 Troubleshooting

### API Connection Issues
```
Error: 451 Client Error (Binance)
Error: 403 Forbidden (Bybit)
```
**Solusi**:
1. Gunakan VPN jika terkena geo-blocking
2. Jalankan mode demo untuk testing
3. Periksa koneksi internet
4. Tunggu beberapa menit jika terkena rate limiting

### Unicode/Emoji Errors
```
UnicodeEncodeError: 'charmap' codec can't encode character
```
**Solusi**: Sudah diperbaiki dengan encoding UTF-8 dan penggantian emoji dengan text

### Port Already in Use
```
OSError: [Errno 48] Address already in use
```
**Solusi**:
1. Tutup program yang sedang berjalan
2. Ganti port di config: `app.run(port=5001)`
3. Kill process: `taskkill /f /im python.exe`

## 📈 Performance Monitoring

### Target Metrics
- **Scan Speed**: 20+ tokens per second
- **Memory Usage**: <2GB RAM
- **Response Time**: <3 seconds
- **Update Frequency**: 30 seconds
- **Token Coverage**: 300+ trading pairs

### Log Files
- **Location**: `arbitrage.log` (mode normal), `demo_arbitrage.log` (mode demo)
- **Rotation**: 10MB per file, 5 backup files
- **Levels**: INFO, WARNING, ERROR

## 🔒 Security & Best Practices

### Data Validation
- Input sanitization untuk semua user inputs
- Price validation (harga positif, range wajar)
- Volume validation (minimum untuk likuiditas)
- Symbol format validation

### Rate Limiting
- Binance: 1200 requests/minute
- Bybit: 120 requests/minute
- Automatic backoff pada rate limit
- Connection pooling untuk efisiensi

### Risk Management
- Profit range filtering (0.5% - 200%)
- Volume minimum requirement ($10,000)
- Trading fee calculation (0.1% per transaksi)
- Network fee consideration

## 🎛️ Advanced Configuration

### Custom Symbol Lists
Edit `priority_symbols` di config.py:
```python
'priority_symbols': [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT',
    # Tambahkan symbol lain sesuai kebutuhan
]
```

### Performance Tuning
```python
PERFORMANCE_CONFIG = {
    'max_workers': 10,        # Thread pool size
    'cache_expiry': 30,       # Cache duration (seconds)
    'max_memory_usage': 2048, # Memory limit (MB)
    'target_scan_speed': 20   # Tokens per second
}
```

## 📞 Support & Contact

### Author Information
- **Author**: BOBACHEESE
- **GitHub**: https://github.com/bobacheese
- **Twitter**: https://twitter.com/bobacheese
- **Telegram**: https://t.me/bobacheese

### Technical Support
1. Check logs untuk error details
2. Gunakan mode demo untuk isolasi masalah
3. Update dependencies: `pip install -r requirements.txt --upgrade`
4. Restart program jika mengalami memory leak

## 📄 License & Disclaimer

### License
MIT License - Bebas digunakan untuk tujuan edukasi dan komersial

### Disclaimer
⚠️ **PERINGATAN PENTING**:
- Program ini hanya untuk deteksi peluang, bukan trading otomatis
- Selalu verifikasi data sebelum melakukan trading
- Pertimbangkan slippage, network fees, dan market volatility
- Trading cryptocurrency memiliki risiko tinggi
- Gunakan hanya dana yang siap Anda rugikan

---

**🙏 Terima kasih telah menggunakan CryptoArb Pro!**  
**💡 Happy Trading & Stay Safe! 🚀**
