<!DOCTYPE html>
<html>
<head>
    <title>Console Monitor</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 1px 0; font-size: 11px; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
        .debug { color: #888; }
        #console-output { height: 400px; overflow-y: auto; border: 1px solid #333; padding: 10px; }
        .controls { margin: 10px 0; }
        .btn { margin: 5px; padding: 5px 10px; background: #333; color: #0f0; border: 1px solid #0f0; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🖥️ Console Monitor for Manual Analysis</h1>
    
    <div class="controls">
        <button class="btn" onclick="clearLog()">🗑️ Clear</button>
        <button class="btn" onclick="testMainPage()">🔍 Test Main Page</button>
        <button class="btn" onclick="openMainPage()">🚀 Open Main Page</button>
        <button class="btn" onclick="testAPI()">🌐 Test API</button>
    </div>
    
    <div id="console-output"></div>
    
    <script>
        const output = document.getElementById('console-output');
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        // Capture console output
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            log(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log(args.join(' '), 'warn');
        };
        
        function clearLog() {
            output.innerHTML = '';
        }
        
        async function testAPI() {
            log('=== TESTING API ===', 'info');
            
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                log(`API Status: ${data.success}`, data.success ? 'success' : 'error');
                log(`Analysis Running: ${data.analysis_status.is_running}`, 'info');
            } catch (error) {
                log(`API Error: ${error.message}`, 'error');
            }
        }
        
        function testMainPage() {
            log('=== TESTING MAIN PAGE ACCESS ===', 'info');
            
            // Create hidden iframe to test main page
            const iframe = document.createElement('iframe');
            iframe.src = '/';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const btn = iframeDoc.getElementById('start-analysis-btn');
                    const statusText = iframeDoc.querySelector('.status-text');
                    const statusIndicator = iframeDoc.getElementById('analysis-status-indicator');
                    
                    log(`Button found: ${!!btn}`, btn ? 'success' : 'error');
                    log(`Status text found: ${!!statusText}`, statusText ? 'success' : 'error');
                    log(`Status indicator found: ${!!statusIndicator}`, statusIndicator ? 'success' : 'error');
                    
                    if (btn) {
                        log(`Button text: "${btn.textContent.trim()}"`, 'info');
                        log(`Button disabled: ${btn.disabled}`, 'info');
                        log(`Button className: "${btn.className}"`, 'info');
                    }
                    
                    if (statusText) {
                        log(`Status text: "${statusText.textContent.trim()}"`, 'info');
                    }
                    
                    // Check if app instance exists
                    const appInstance = iframe.contentWindow.binanceApp;
                    log(`App instance exists: ${!!appInstance}`, appInstance ? 'success' : 'error');
                    
                    if (appInstance) {
                        log(`App loading complete: ${appInstance.loadingComplete}`, 'info');
                        
                        // Try to call updateAnalysisUI
                        if (typeof appInstance.updateAnalysisUI === 'function') {
                            log('Calling updateAnalysisUI("idle")...', 'info');
                            appInstance.updateAnalysisUI('idle');
                            log('updateAnalysisUI called successfully', 'success');
                        } else {
                            log('updateAnalysisUI method not found', 'error');
                        }
                    }
                    
                } catch (error) {
                    log(`Error accessing iframe content: ${error.message}`, 'error');
                }
                
                // Remove iframe after test
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 5000);
            };
            
            iframe.onerror = function() {
                log('Failed to load main page in iframe', 'error');
            };
        }
        
        function openMainPage() {
            log('Opening main page in new window...', 'info');
            const newWindow = window.open('/', '_blank');
            
            if (newWindow) {
                log('New window opened successfully', 'success');
                
                // Try to monitor the new window
                setTimeout(() => {
                    try {
                        const btn = newWindow.document.getElementById('start-analysis-btn');
                        log(`Button in new window: ${!!btn}`, btn ? 'success' : 'error');
                        
                        if (btn) {
                            log('Attempting to click button in new window...', 'info');
                            btn.click();
                            log('Button clicked!', 'success');
                        }
                    } catch (error) {
                        log(`Error accessing new window: ${error.message}`, 'error');
                    }
                }, 3000);
            } else {
                log('Failed to open new window (popup blocked?)', 'error');
            }
        }
        
        // Start monitoring
        log('🖥️ Console monitor started', 'success');
        log('Monitoring console output from main application...', 'info');
        
        // Test API immediately
        setTimeout(testAPI, 1000);
        
    </script>
</body>
</html>
