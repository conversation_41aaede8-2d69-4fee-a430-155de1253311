# -*- coding: utf-8 -*-
"""
Konfigurasi untuk CryptoArb Pro
"""

# API Configuration
API_CONFIG = {
    'binance': {
        'base_url': 'https://api.binance.com/api/v3',
        'rate_limit': 1200,  # requests per minute
        'timeout': 10,
        'headers': {
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        }
    },
    'bybit': {
        'base_url': 'https://api.bybit.com/v5',
        'rate_limit': 120,  # requests per minute
        'timeout': 10,
        'headers': {
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        }
    }
}

# Trading Configuration
TRADING_CONFIG = {
    'min_profit_percentage': 0.5,
    'max_profit_percentage': 200.0,
    'min_volume_24h': 10000,  # USD
    'trading_fee_percentage': 0.1,  # 0.1% per transaction
    'scan_interval': 30,  # seconds
    'priority_symbols': [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
        'DOGEUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'DOTUSDT',
        'LINKUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'LTCUSDT',
        'TRXUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT',
        'SHIBUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'BONKUSDT', 'WIFUSDT'
    ]
}

# Network Configuration
NETWORK_CONFIG = {
    'supported_networks': ['ETH', 'BSC', 'TRC20', 'POLYGON', 'ARBITRUM'],
    'transfer_times': {
        'ETH': '5-15 menit',
        'BSC': '1-3 menit', 
        'TRC20': '1-5 menit',
        'POLYGON': '1-2 menit',
        'ARBITRUM': '1-5 menit'
    }
}

# UI Configuration
UI_CONFIG = {
    'theme': 'dark_futuristic',
    'language': 'id',
    'auto_refresh': True,
    'notification_threshold': 5.0,  # profit percentage for notifications
    'max_opportunities_display': 100
}

# Performance Configuration
PERFORMANCE_CONFIG = {
    'max_workers': 10,
    'cache_expiry': 30,  # seconds
    'max_memory_usage': 2048,  # MB
    'target_scan_speed': 20,  # tokens per second
    'max_symbols': 300
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_encoding': 'utf-8',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Flask Configuration
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False,
    'threaded': True,
    'use_reloader': False
}
