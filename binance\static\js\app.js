/**
 * Binance Signal Generator Pro - Frontend Application
 * Real-time trading signals dengan Chart.js integration
 * Author: BOBACHEESE
 */

class BinanceSignalApp {
    constructor() {
        this.signals = [];
        this.pairs = [];
        this.currentChart = null;
        this.refreshInterval = null;
        this.progressInterval = null; // For manual analysis progress monitoring
        this.autoRefresh = true;
        this.refreshIntervalTime = 30000; // 30 seconds
        this.loadingComplete = false;
        this.debugMode = true; // Enable comprehensive debugging

        console.log('[BINANCE APP] Initializing application...');
        this.init();
    }

    async init() {
        try {
            console.log('[BINANCE APP] Starting initialization sequence...');

            // Step 1: Setup event listeners
            console.log('[BINANCE APP] Setting up event listeners...');
            this.setupEventListeners();

            // Step 2: Start clock
            console.log('[BINANCE APP] Starting clock...');
            this.startClock();

            // Step 3: Setup emergency fallback (reduced to 8 seconds)
            console.log('[BINANCE APP] Setting up emergency fallback timer...');
            this.setupEmergencyFallback();

            // Step 4: Load initial data with comprehensive error handling
            console.log('[BINANCE APP] Loading initial data...');
            await this.loadInitialDataWithRetry();

            // Step 5: Hide loading screen and start auto-refresh
            console.log('[BINANCE APP] Finalizing initialization...');
            this.completeInitialization();

        } catch (error) {
            console.error('[BINANCE APP] Critical initialization error:', error);
            this.handleInitializationFailure(error);
        }
    }

    setupEmergencyFallback() {
        // Multiple fallback timers for different scenarios
        setTimeout(() => {
            if (!this.loadingComplete) {
                console.warn('[BINANCE APP] Emergency fallback triggered at 5 seconds');
                this.forceShowMainInterface('Emergency timeout - 5 seconds');
            }
        }, 5000);

        setTimeout(() => {
            if (!this.loadingComplete) {
                console.warn('[BINANCE APP] Final emergency fallback triggered at 8 seconds');
                this.forceShowMainInterface('Final emergency timeout - 8 seconds');
            }
        }, 8000);
    }

    async loadInitialDataWithRetry() {
        const maxRetries = 3;
        let attempt = 0;

        while (attempt < maxRetries && !this.loadingComplete) {
            try {
                attempt++;
                console.log(`[BINANCE APP] Data loading attempt ${attempt}/${maxRetries}`);

                // Load with shorter timeouts and better error handling
                await this.loadInitialDataSafe();

                console.log('[BINANCE APP] Initial data loaded successfully');
                return;

            } catch (error) {
                console.error(`[BINANCE APP] Data loading attempt ${attempt} failed:`, error);

                if (attempt === maxRetries) {
                    console.warn('[BINANCE APP] All data loading attempts failed, proceeding with limited functionality');
                    this.showNotification('Loaded with limited functionality due to connection issues', 'warning');
                    return;
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    completeInitialization() {
        if (!this.loadingComplete) {
            this.loadingComplete = true;
            console.log('[BINANCE APP] Initialization completed successfully');

            // Hide loading screen first
            this.hideLoadingScreen();

            // Start auto refresh
            this.startAutoRefresh();

            // Initialize manual analysis UI with proper sequencing
            this.initializeManualAnalysisUI();
        }
    }

    initializeManualAnalysisUI() {
        console.log('[MANUAL] Starting manual analysis UI initialization...');

        // Wait for loading screen to be fully hidden
        setTimeout(() => {
            this.ensureManualAnalysisUIReady();
        }, 1500); // Wait for loading screen animation to complete
    }

    ensureManualAnalysisUIReady() {
        console.log('[MANUAL] Ensuring manual analysis UI is ready...');

        // Check if main container is visible
        const mainContainer = document.getElementById('main-container');
        const loadingScreen = document.getElementById('loading-screen');

        if (mainContainer) {
            const mainDisplay = window.getComputedStyle(mainContainer).display;
            console.log(`[MANUAL] Main container display: ${mainDisplay}`);

            if (mainDisplay === 'none') {
                console.log('[MANUAL] Main container not visible, forcing display');
                mainContainer.style.display = 'flex';
            }
        }

        if (loadingScreen) {
            const loadingDisplay = window.getComputedStyle(loadingScreen).display;
            console.log(`[MANUAL] Loading screen display: ${loadingDisplay}`);

            if (loadingDisplay !== 'none') {
                console.log('[MANUAL] Loading screen still visible, forcing hide');
                loadingScreen.style.display = 'none';
            }
        }

        // Now initialize the UI with retry mechanism
        this.updateAnalysisUIWithRetry('idle', 0);
    }

    updateAnalysisUIWithRetry(state, attempt = 0) {
        const maxAttempts = 10;
        console.log(`[MANUAL] Attempting to update analysis UI (attempt ${attempt + 1}/${maxAttempts})`);

        const btn = document.getElementById('start-analysis-btn');
        const statusText = document.querySelector('.status-text');
        const statusIndicator = document.getElementById('analysis-status-indicator');

        if (btn && statusText && statusIndicator) {
            console.log('[MANUAL] All elements found, updating UI...');
            this.updateAnalysisUI(state);
            console.log('[MANUAL] Manual analysis UI initialized successfully!');
            return;
        }

        if (attempt < maxAttempts - 1) {
            console.log(`[MANUAL] Elements not ready, retrying in 500ms...`);
            setTimeout(() => {
                this.updateAnalysisUIWithRetry(state, attempt + 1);
            }, 500);
        } else {
            console.error('[MANUAL] Failed to initialize manual analysis UI after all attempts');
            console.error(`[MANUAL] Final state - btn: ${!!btn}, statusText: ${!!statusText}, statusIndicator: ${!!statusIndicator}`);
        }
    }

    handleInitializationFailure(error) {
        console.error('[BINANCE APP] Initialization failed completely:', error);
        this.forceShowMainInterface('Initialization failed: ' + error.message);
    }

    forceShowMainInterface(reason) {
        if (!this.loadingComplete) {
            this.loadingComplete = true;
            console.warn('[BINANCE APP] Force showing main interface:', reason);

            const loadingScreen = document.getElementById('loading-screen');
            const mainContainer = document.getElementById('main-container');

            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }

            if (mainContainer) {
                mainContainer.style.display = 'flex';
            }

            this.showNotification(reason, 'warning');

            // Try to start basic functionality
            try {
                this.startClock();
                this.setupEventListeners();
            } catch (e) {
                console.error('[BINANCE APP] Error starting basic functionality:', e);
            }
        }
    }
    
    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });
        
        // Manual analysis control
        const startBtn = document.getElementById('start-analysis-btn');
        if (startBtn) {
            console.log('[MANUAL] Start analysis button found, adding event listener');
            startBtn.addEventListener('click', () => {
                console.log('[MANUAL] Start analysis button clicked');
                this.startManualAnalysis();
            });
        } else {
            console.error('[MANUAL] Start analysis button not found during event listener setup');
        }

        // Refresh signals
        document.getElementById('refresh-signals')?.addEventListener('click', () => this.loadSignals());

        // Signal filters
        document.getElementById('signal-filter')?.addEventListener('change', () => this.filterSignals());
        document.getElementById('confidence-filter')?.addEventListener('input', (e) => {
            document.getElementById('confidence-value').textContent = e.target.value + '%';
            this.filterSignals();
        });
        
        // Pair search
        document.getElementById('search-pair')?.addEventListener('click', () => this.searchPair());
        document.getElementById('pair-search')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchPair();
        });
        
        // Chart controls
        document.getElementById('chart-symbol')?.addEventListener('change', () => this.loadChart());
        document.getElementById('chart-interval')?.addEventListener('change', () => this.loadChart());
        
        // Author modal
        document.getElementById('author-btn')?.addEventListener('click', () => this.showAuthorModal());
        document.getElementById('close-author-modal')?.addEventListener('click', () => this.hideAuthorModal());
        
        // Settings
        document.getElementById('save-api-key')?.addEventListener('click', () => this.saveApiKey());
        document.getElementById('auto-refresh')?.addEventListener('change', (e) => {
            this.autoRefresh = e.target.checked;
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
        document.getElementById('refresh-interval')?.addEventListener('change', (e) => {
            this.refreshIntervalTime = parseInt(e.target.value) * 1000;
            if (this.autoRefresh) {
                this.stopAutoRefresh();
                this.startAutoRefresh();
            }
        });
        
        // Modal close on outside click
        document.getElementById('author-modal')?.addEventListener('click', (e) => {
            if (e.target.id === 'author-modal') this.hideAuthorModal();
        });
    }
    
    async loadInitialDataSafe() {
        const shortTimeout = 8000; // 8 second timeout for each operation

        try {
            console.log('[BINANCE APP] Loading status...');
            const statusTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Status loading timeout')), shortTimeout)
            );
            await Promise.race([this.updateStatusSafe(), statusTimeout]);
            console.log('[BINANCE APP] Status loaded successfully');

        } catch (error) {
            console.warn('[BINANCE APP] Status loading failed:', error.message);
            // Continue anyway - status is not critical for basic functionality
        }

        try {
            console.log('[BINANCE APP] Loading pairs...');
            const pairsTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Pairs loading timeout')), shortTimeout)
            );
            await Promise.race([this.loadPairsSafe(), pairsTimeout]);
            console.log('[BINANCE APP] Pairs loaded successfully');

        } catch (error) {
            console.warn('[BINANCE APP] Pairs loading failed:', error.message);
            // Continue anyway - pairs are not critical for basic functionality
        }

        try {
            console.log('[BINANCE APP] Loading signals...');
            const signalsTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Signals loading timeout')), shortTimeout)
            );
            await Promise.race([this.loadSignalsSafe(), signalsTimeout]);
            console.log('[BINANCE APP] Signals loaded successfully');

        } catch (error) {
            console.warn('[BINANCE APP] Signals loading failed:', error.message);
            // Show empty state instead of failing completely
            this.renderEmptySignalsState();
        }

        console.log('[BINANCE APP] Initial data loading completed');
    }

    async updateStatusSafe() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch('/api/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update status indicators
                const binanceStatus = document.getElementById('binance-status');
                const aiStatus = document.getElementById('ai-status');

                if (binanceStatus) {
                    binanceStatus.className = `status-dot ${data.status.binance_api === 'healthy' ? 'healthy' : 'error'}`;
                }

                if (aiStatus) {
                    aiStatus.className = `status-dot ${data.status.ai_service === 'available' ? 'healthy' : 'error'}`;
                }

                // Update stats
                if (data.status.stats) {
                    this.updateStats(data.status.stats);
                }
            } else {
                throw new Error(data.error || 'Status update failed');
            }
        } catch (error) {
            console.error('[BINANCE APP] Status update error:', error);
            // Set error status indicators
            const binanceStatus = document.getElementById('binance-status');
            const aiStatus = document.getElementById('ai-status');

            if (binanceStatus) binanceStatus.className = 'status-dot error';
            if (aiStatus) aiStatus.className = 'status-dot error';

            throw error;
        }
    }
    
    async updateStatus() {
        try {
            const response = await fetch('/api/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 10000 // 10 second timeout
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update status indicators
                const binanceStatus = document.getElementById('binance-status');
                const aiStatus = document.getElementById('ai-status');

                if (binanceStatus) {
                    binanceStatus.className = `status-dot ${data.status.binance_api === 'healthy' ? 'healthy' : 'error'}`;
                }

                if (aiStatus) {
                    aiStatus.className = `status-dot ${data.status.ai_service === 'available' ? 'healthy' : 'error'}`;
                }

                // Update stats
                if (data.status.stats) {
                    this.updateStats(data.status.stats);
                }
            } else {
                throw new Error(data.error || 'Status update failed');
            }
        } catch (error) {
            console.error('Error updating status:', error);
            // Set error status indicators
            const binanceStatus = document.getElementById('binance-status');
            const aiStatus = document.getElementById('ai-status');

            if (binanceStatus) binanceStatus.className = 'status-dot error';
            if (aiStatus) aiStatus.className = 'status-dot error';
        }
    }
    
    updateStats(stats) {
        const totalPairs = document.getElementById('total-pairs');
        const signalsCount = document.getElementById('signals-count');
        const analysisTime = document.getElementById('analysis-time');
        
        if (totalPairs) totalPairs.textContent = stats.total_analyzed || '-';
        if (signalsCount) signalsCount.textContent = stats.signals_generated || '-';
        if (analysisTime) analysisTime.textContent = stats.analysis_time ? 
            `${stats.analysis_time.toFixed(1)}s` : '-';
    }
    
    async loadSignals() {
        try {
            this.showLoading('signals-grid');

            const response = await fetch('/api/signals?limit=50');
            const data = await response.json();

            if (data.success) {
                this.signals = data.data;
                this.renderSignals();
                this.updateStats(data.stats);
                this.showNotification(`Loaded ${data.count} signals`, 'success');
            } else {
                throw new Error(data.error || 'Failed to load signals');
            }
        } catch (error) {
            console.error('Error loading signals:', error);
            this.showNotification('Error loading signals', 'error');
        } finally {
            this.hideLoading('signals-grid');
        }
    }

    async loadSignalsSafe() {
        try {
            console.log('[BINANCE APP] Fetching signals from API...');

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000);

            const response = await fetch('/api/signals?limit=50', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.signals = data.data || [];
                this.renderSignals();
                this.updateStats(data.stats);
                console.log(`[BINANCE APP] Loaded ${data.count || 0} signals successfully`);
            } else {
                throw new Error(data.error || 'Failed to load signals');
            }
        } catch (error) {
            console.error('[BINANCE APP] Signals loading error:', error);
            this.renderEmptySignalsState();
            throw error;
        }
    }

    renderEmptySignalsState() {
        const container = document.getElementById('signals-grid');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📊</div>
                    <div class="empty-text">Signals sedang dimuat atau tidak tersedia</div>
                    <button onclick="window.binanceApp.loadSignals()" class="refresh-btn">
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">Coba Lagi</span>
                    </button>
                </div>
            `;
        }
    }
    
    renderSignals() {
        const container = document.getElementById('signals-grid');
        if (!container) return;
        
        if (this.signals.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📊</div>
                    <div class="empty-text">Tidak ada signal tersedia</div>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.signals.map(signal => this.createSignalCard(signal)).join('');
    }
    
    createSignalCard(signal) {
        const signalTypeClass = signal.signal_type.toLowerCase().replace('_', '-');
        const confidenceWidth = Math.max(signal.confidence, 5); // Minimum 5% width
        
        return `
            <div class="signal-card" data-signal-type="${signal.signal_type}" data-confidence="${signal.confidence}">
                <div class="signal-header">
                    <div class="signal-symbol">${signal.symbol}</div>
                    <div class="signal-type ${signalTypeClass}">${signal.signal_type.replace('_', ' ')}</div>
                </div>
                
                <div class="signal-confidence">
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${confidenceWidth}%"></div>
                    </div>
                    <div class="confidence-text">${signal.confidence.toFixed(1)}%</div>
                </div>
                
                <div class="signal-details">
                    <div class="detail-item">
                        <div class="detail-label">Entry Points</div>
                        <div class="detail-value">$${signal.entry_points.map(p => p.toFixed(4)).join(', $')}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Stop Loss</div>
                        <div class="detail-value">$${signal.stop_loss.toFixed(4)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Take Profit</div>
                        <div class="detail-value">$${signal.take_profit.map(p => p.toFixed(4)).join(', $')}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Risk Level</div>
                        <div class="detail-value">${signal.risk_level}</div>
                    </div>
                </div>
                
                <div class="signal-reasoning">${signal.reasoning}</div>
                
                ${signal.ai_analysis ? `<div class="signal-ai-analysis">${signal.ai_analysis}</div>` : ''}
                
                <div class="signal-timestamp">${this.formatTimestamp(signal.timestamp)}</div>
            </div>
        `;
    }
    
    filterSignals() {
        const signalFilter = document.getElementById('signal-filter')?.value || 'all';
        const confidenceFilter = parseInt(document.getElementById('confidence-filter')?.value || '0');
        
        const cards = document.querySelectorAll('.signal-card');
        cards.forEach(card => {
            const signalType = card.dataset.signalType;
            const confidence = parseFloat(card.dataset.confidence);
            
            const typeMatch = signalFilter === 'all' || signalType === signalFilter;
            const confidenceMatch = confidence >= confidenceFilter;
            
            card.style.display = typeMatch && confidenceMatch ? 'block' : 'none';
        });
    }
    
    async loadPairs() {
        try {
            const response = await fetch('/api/pairs?limit=100');
            const data = await response.json();

            if (data.success) {
                this.pairs = data.data;
                this.populateChartSymbols();
            }
        } catch (error) {
            console.error('Error loading pairs:', error);
        }
    }

    async loadPairsSafe() {
        try {
            console.log('[BINANCE APP] Fetching pairs from API...');

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000);

            const response = await fetch('/api/pairs?limit=100', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.pairs = data.data || [];
                this.populateChartSymbols();
                console.log(`[BINANCE APP] Loaded ${this.pairs.length} pairs successfully`);
            } else {
                throw new Error(data.error || 'Failed to load pairs');
            }
        } catch (error) {
            console.error('[BINANCE APP] Pairs loading error:', error);
            this.pairs = []; // Set empty array as fallback
            throw error;
        }
    }
    
    populateChartSymbols() {
        const select = document.getElementById('chart-symbol');
        if (!select || this.pairs.length === 0) return;
        
        select.innerHTML = '<option value="">Pilih Symbol</option>' +
            this.pairs.slice(0, 50).map(pair => 
                `<option value="${pair.symbol}">${pair.symbol}</option>`
            ).join('');
    }
    
    async searchPair() {
        const input = document.getElementById('pair-search');
        const symbol = input?.value?.trim().toUpperCase();
        
        if (!symbol) {
            this.showNotification('Masukkan symbol pair', 'warning');
            return;
        }
        
        try {
            this.showLoading('analysis-result');
            
            const response = await fetch(`/api/pair/${symbol}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderPairAnalysis(data.data);
                this.showNotification(`Analysis completed for ${symbol}`, 'success');
            } else {
                throw new Error(data.error || 'Failed to analyze pair');
            }
        } catch (error) {
            console.error('Error analyzing pair:', error);
            this.showNotification(`Error analyzing ${symbol}`, 'error');
            this.renderPairAnalysisError(error.message);
        } finally {
            this.hideLoading('analysis-result');
        }
    }
    
    renderPairAnalysis(signal) {
        const container = document.getElementById('analysis-result');
        if (!container) return;
        
        container.innerHTML = this.createSignalCard(signal);
    }
    
    renderPairAnalysisError(error) {
        const container = document.getElementById('analysis-result');
        if (!container) return;

        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">❌</div>
                <div class="empty-text">Error: ${error}</div>
            </div>
        `;
    }

    async loadChart() {
        const symbol = document.getElementById('chart-symbol')?.value;
        const interval = document.getElementById('chart-interval')?.value || '1h';

        if (!symbol) {
            this.showNotification('Pilih symbol untuk chart', 'warning');
            return;
        }

        try {
            const response = await fetch(`/api/candlesticks/${symbol}?interval=${interval}&limit=100`);
            const data = await response.json();

            if (data.success) {
                this.renderChart(data.data, symbol, interval);
                this.showNotification(`Chart loaded for ${symbol}`, 'success');
            } else {
                throw new Error(data.error || 'Failed to load chart data');
            }
        } catch (error) {
            console.error('Error loading chart:', error);
            this.showNotification('Error loading chart', 'error');
        }
    }

    renderChart(candlesticks, symbol, interval) {
        const canvas = document.getElementById('price-chart');
        if (!canvas) return;

        // Destroy existing chart
        if (this.currentChart) {
            this.currentChart.destroy();
        }

        const ctx = canvas.getContext('2d');

        // Prepare data
        const chartData = candlesticks.map(candle => ({
            x: new Date(candle.open_time),
            o: candle.open_price,
            h: candle.high_price,
            l: candle.low_price,
            c: candle.close_price,
            v: candle.volume
        }));

        // Chart configuration
        this.currentChart = new Chart(ctx, {
            type: 'candlestick',
            data: {
                datasets: [{
                    label: symbol,
                    data: chartData,
                    borderColor: '#00ffff',
                    backgroundColor: 'rgba(0, 255, 255, 0.1)',
                    borderWidth: 1,
                    barThickness: 'flex',
                    maxBarThickness: 8,
                    color: {
                        up: '#10b981',
                        down: '#ef4444',
                        unchanged: '#6b7280'
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: {
                            color: '#ffffff',
                            font: {
                                family: 'Inter'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#00ffff',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                return new Date(context[0].parsed.x).toLocaleString('id-ID');
                            },
                            label: function(context) {
                                const data = context.parsed;
                                return [
                                    `Open: $${data.o.toFixed(4)}`,
                                    `High: $${data.h.toFixed(4)}`,
                                    `Low: $${data.l.toFixed(4)}`,
                                    `Close: $${data.c.toFixed(4)}`,
                                    `Volume: ${data.v.toLocaleString()}`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: interval === '1d' ? 'day' : 'hour',
                            displayFormats: {
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa',
                            font: {
                                family: 'Inter'
                            }
                        }
                    },
                    y: {
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa',
                            font: {
                                family: 'Inter'
                            },
                            callback: function(value) {
                                return '$' + value.toFixed(4);
                            }
                        }
                    }
                }
            }
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`)?.classList.add('active');

        // Load data for specific tabs
        if (tabName === 'charts' && this.pairs.length === 0) {
            this.loadPairs();
        }
    }

    showAuthorModal() {
        const modal = document.getElementById('author-modal');
        if (modal) {
            modal.classList.add('show');
            modal.style.display = 'flex';
        }
    }

    hideAuthorModal() {
        const modal = document.getElementById('author-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
    }

    saveApiKey() {
        const input = document.getElementById('gemini-api-key');
        const apiKey = input?.value?.trim();

        if (!apiKey) {
            this.showNotification('Masukkan API key Gemini', 'warning');
            return;
        }

        // Save to localStorage (in production, use secure storage)
        localStorage.setItem('gemini_api_key', apiKey);
        this.showNotification('API key saved successfully', 'success');

        // Clear input for security
        if (input) input.value = '';
    }

    startClock() {
        const updateClock = () => {
            const now = new Date();
            const timeString = now.toLocaleString('id-ID', {
                timeZone: 'Asia/Jakarta',
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.textContent = timeString + ' WIB';
            }
        };

        updateClock();
        setInterval(updateClock, 1000);
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            if (this.autoRefresh) {
                this.loadSignals();
                this.updateStatus();
            }
        }, this.refreshIntervalTime);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.add('loading');
        }
    }

    hideLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('loading');
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainContainer = document.getElementById('main-container');

        if (loadingScreen && mainContainer) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                mainContainer.style.display = 'flex';
                mainContainer.style.animation = 'fadeInUp 0.8s ease-out';
            }, 500);
        }
    }

    forceHideLoadingScreen() {
        // Force hide loading screen after maximum wait time
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            const mainContainer = document.getElementById('main-container');

            if (loadingScreen && loadingScreen.style.display !== 'none') {
                console.warn('Force hiding loading screen due to timeout');
                loadingScreen.style.display = 'none';
                if (mainContainer) {
                    mainContainer.style.display = 'flex';
                }
                this.showNotification('Application loaded with limited functionality', 'warning');
            }
        }, 15000); // 15 seconds maximum loading time
    }

    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">${icons[type] || icons.info}</div>
                <div class="notification-text">
                    <div class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                    <div class="notification-message">${message}</div>
                </div>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add close functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.removeNotification(notification);
        });

        container.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);
    }

    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('id-ID', {
            timeZone: 'Asia/Jakarta',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Manual Analysis Control Functions
    async startManualAnalysis() {
        try {
            console.log('[MANUAL] Starting manual analysis...');

            const response = await fetch('/api/start-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(data.message, 'success');
                this.updateAnalysisUI('running');
                this.startProgressMonitoring();
            } else {
                throw new Error(data.error || 'Failed to start analysis');
            }

        } catch (error) {
            console.error('[MANUAL] Error starting analysis:', error);
            this.showNotification('Error memulai analisis: ' + error.message, 'error');
            this.updateAnalysisUI('error');
        }
    }

    startProgressMonitoring() {
        // Clear any existing progress monitoring
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start monitoring progress every 2 seconds
        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch('/api/analysis-progress');
                const data = await response.json();

                if (data.success) {
                    this.updateProgressUI(data.progress);

                    // Stop monitoring if analysis is complete or failed
                    if (!data.progress.is_running) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;

                        if (data.progress.error_message) {
                            this.updateAnalysisUI('error');
                            this.showNotification('Analisis gagal: ' + data.progress.error_message, 'error');
                        } else {
                            this.updateAnalysisUI('completed');
                            this.showNotification('Analisis selesai! Memuat signals...', 'success');
                            // Reload signals after analysis completion
                            setTimeout(() => this.loadSignals(), 1000);
                        }
                    }
                }
            } catch (error) {
                console.error('[MANUAL] Error monitoring progress:', error);
            }
        }, 2000);
    }

    updateAnalysisUI(state) {
        console.log(`[MANUAL] updateAnalysisUI called with state: ${state}`);

        const btn = document.getElementById('start-analysis-btn');
        const statusText = document.querySelector('.status-text');
        const statusIndicator = document.getElementById('analysis-status-indicator');
        const progressContainer = document.getElementById('progress-container');

        console.log(`[MANUAL] Elements found - btn: ${!!btn}, statusText: ${!!statusText}, statusIndicator: ${!!statusIndicator}, progressContainer: ${!!progressContainer}`);

        if (!btn || !statusText || !statusIndicator) {
            console.error('[MANUAL] Required elements not found in updateAnalysisUI');
            return;
        }

        console.log('[MANUAL] All elements found, proceeding with UI update...');

        // Reset classes
        btn.className = 'start-analysis-btn';
        statusIndicator.className = 'status-indicator';

        switch (state) {
            case 'idle':
                btn.innerHTML = '<span class="btn-icon">▶️</span><span class="btn-text">Mulai Analisis</span>';
                btn.disabled = false;
                statusText.textContent = 'Siap untuk analisis';
                statusIndicator.classList.add('idle');
                if (progressContainer) progressContainer.style.display = 'none';
                break;

            case 'running':
                btn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">Sedang Analisis...</span>';
                btn.disabled = true;
                btn.classList.add('analyzing');
                statusText.textContent = 'Analisis sedang berjalan';
                statusIndicator.classList.add('running');
                if (progressContainer) progressContainer.style.display = 'block';
                break;

            case 'completed':
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Analisis Ulang</span>';
                btn.disabled = false;
                btn.classList.add('completed');
                statusText.textContent = 'Analisis selesai';
                statusIndicator.classList.add('completed');
                if (progressContainer) progressContainer.style.display = 'none';
                break;

            case 'error':
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Coba Lagi</span>';
                btn.disabled = false;
                statusText.textContent = 'Analisis gagal';
                statusIndicator.classList.add('error');
                if (progressContainer) progressContainer.style.display = 'none';
                break;
        }
    }

    updateProgressUI(progress) {
        const progressText = document.getElementById('progress-text');
        const progressPercentage = document.getElementById('progress-percentage');
        const progressFill = document.getElementById('progress-fill');
        const currentPair = document.getElementById('current-pair');
        const estimatedTime = document.getElementById('estimated-time');

        if (!progress.is_running) return;

        // Calculate percentage
        const percentage = progress.total_pairs > 0 ?
            Math.round((progress.progress / progress.total_pairs) * 100) : 0;

        // Update progress elements
        if (progressText) {
            progressText.textContent = `Menganalisis... ${progress.progress}/${progress.total_pairs} pairs`;
        }

        if (progressPercentage) {
            progressPercentage.textContent = `${percentage}%`;
        }

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (currentPair) {
            currentPair.textContent = progress.current_pair || '-';
        }

        if (estimatedTime && progress.estimated_completion) {
            const estimated = new Date(progress.estimated_completion);
            const now = new Date();
            const remaining = Math.max(0, Math.round((estimated - now) / 1000));

            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60);
                const seconds = remaining % 60;
                estimatedTime.textContent = `~${minutes}:${seconds.toString().padStart(2, '0')} tersisa`;
            } else {
                estimatedTime.textContent = 'Hampir selesai...';
            }
        }
    }
}

// Custom Chart.js candlestick chart type
Chart.register({
    id: 'candlestick',
    beforeInit: function(chart) {
        if (chart.config.type === 'candlestick') {
            chart.config.type = 'bar';
            chart.config.data.datasets.forEach(dataset => {
                dataset.type = 'candlestick';
            });
        }
    }
});

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('[BINANCE APP] DOM loaded, starting application...');

    // Verify critical DOM elements exist
    const loadingScreen = document.getElementById('loading-screen');
    const mainContainer = document.getElementById('main-container');

    if (!loadingScreen || !mainContainer) {
        console.error('[BINANCE APP] Critical DOM elements missing!', {
            loadingScreen: !!loadingScreen,
            mainContainer: !!mainContainer
        });

        // Force show main container if loading screen is missing
        if (mainContainer) {
            mainContainer.style.display = 'flex';
        }
        return;
    }

    // Add multiple emergency fallbacks with different timings
    setTimeout(() => {
        if (loadingScreen && loadingScreen.style.display !== 'none') {
            console.warn('[BINANCE APP] Emergency fallback #1 triggered at 3 seconds');
            loadingScreen.style.display = 'none';
            mainContainer.style.display = 'flex';
        }
    }, 3000);

    setTimeout(() => {
        if (loadingScreen && loadingScreen.style.display !== 'none') {
            console.warn('[BINANCE APP] Emergency fallback #2 triggered at 6 seconds');
            loadingScreen.style.display = 'none';
            mainContainer.style.display = 'flex';
        }
    }, 6000);

    setTimeout(() => {
        if (loadingScreen && loadingScreen.style.display !== 'none') {
            console.warn('[BINANCE APP] Final emergency fallback triggered at 10 seconds');
            loadingScreen.style.display = 'none';
            mainContainer.style.display = 'flex';
        }
    }, 10000);

    // Initialize the application
    try {
        console.log('[BINANCE APP] Creating BinanceSignalApp instance...');
        window.binanceApp = new BinanceSignalApp();
        console.log('[BINANCE APP] Application instance created successfully');
        console.log('[BINANCE APP] Instance type:', typeof window.binanceApp);
        console.log('[BINANCE APP] Instance constructor:', window.binanceApp.constructor.name);

        // Test if manual analysis elements are accessible
        setTimeout(() => {
            console.log('[BINANCE APP] Testing element access after 2 seconds...');
            const btn = document.getElementById('start-analysis-btn');
            const statusText = document.querySelector('.status-text');
            const statusIndicator = document.getElementById('analysis-status-indicator');

            console.log('[BINANCE APP] Element check results:');
            console.log('  - start-analysis-btn:', !!btn);
            console.log('  - status-text:', !!statusText);
            console.log('  - analysis-status-indicator:', !!statusIndicator);

            if (btn) {
                console.log('  - Button innerHTML:', btn.innerHTML);
                console.log('  - Button disabled:', btn.disabled);
                console.log('  - Button className:', btn.className);
            }
        }, 2000);

    } catch (error) {
        console.error('[BINANCE APP] Failed to create application instance:', error);

        // Force show interface even if app fails to initialize
        if (loadingScreen) loadingScreen.style.display = 'none';
        if (mainContainer) mainContainer.style.display = 'flex';

        // Show error message to user
        if (mainContainer) {
            mainContainer.innerHTML = `
                <div style="padding: 20px; text-align: center; color: #ef4444;">
                    <h2>⚠️ Application Error</h2>
                    <p>Failed to initialize Binance Signal Generator Pro</p>
                    <p>Error: ${error.message}</p>
                    <button onclick="location.reload()" style="padding: 10px 20px; background: #00ffff; color: #000; border: none; border-radius: 5px; cursor: pointer;">
                        Reload Application
                    </button>
                </div>
            `;
        }
    }
});

// Add slideOutRight animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(style);
