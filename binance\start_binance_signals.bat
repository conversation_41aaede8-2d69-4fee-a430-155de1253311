@echo off
REM Binance Signal Generator Pro - Windows Startup Script
REM Author: BOBACHEESE
REM Version: 1.0.0

title Binance Signal Generator Pro - Startup

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🚀 BINANCE SIGNAL GENERATOR PRO 🚀              ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  📊 Real-time Technical Analysis ^& AI-Powered Signals       ║
echo ║  🎯 Target: 500+ Binance Futures Pairs                      ║
echo ║  🧠 60+ Technical Indicators + Google Gemini AI             ║
echo ║  ⚡ Performance: ^<30s analysis, ^<2GB memory                 ║
echo ║  🌐 Web Interface: http://localhost:5001                    ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  Author: BOBACHEESE                                          ║
echo ║  Framework: Flask + HTML/CSS/JavaScript                     ║
echo ║  Environment: Conda on Windows 11                           ║
echo ║  Port: 5001 (menghindari konflik dengan arbitrage bot)      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if we're in the correct directory
if not exist "main.py" (
    echo [ERROR] main.py tidak ditemukan!
    echo [INFO] Pastikan Anda menjalankan script ini dari direktori binance
    echo [INFO] Direktori saat ini: %CD%
    pause
    exit /b 1
)

echo [INFO] Memeriksa Python installation...

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python tidak ditemukan di PATH.
    echo [INFO] Pastikan Python 3.8+ terinstall dan ditambahkan ke PATH
    echo [INFO] Download Python dari: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [INFO] Python installation OK

echo [INFO] Memeriksa dependencies...

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo [ERROR] requirements.txt tidak ditemukan!
    pause
    exit /b 1
)

REM Install dependencies if needed
echo [INFO] Installing/updating dependencies...
pip install -r requirements.txt --quiet --upgrade

if errorlevel 1 (
    echo [ERROR] Gagal menginstall dependencies.
    echo [INFO] Coba jalankan manual: pip install -r requirements.txt
    echo [INFO] Atau gunakan conda: conda install --file requirements.txt
    pause
    exit /b 1
)

echo [INFO] Dependencies OK

REM Check environment file
if not exist ".env" (
    echo [WARNING] File .env tidak ditemukan!
    echo [INFO] Copying .env.example to .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [INFO] File .env telah dibuat dari template
        echo [INFO] Edit file .env untuk konfigurasi API keys
    ) else (
        echo [ERROR] .env.example tidak ditemukan!
        pause
        exit /b 1
    )
)

echo [INFO] Environment configuration OK

REM Run tests (optional)
set /p run_tests="Jalankan tests sebelum startup? (y/N): "
if /i "%run_tests%"=="y" (
    echo [INFO] Running test suite...
    python test_suite.py
    if errorlevel 1 (
        echo [WARNING] Some tests failed, but continuing...
        pause
    ) else (
        echo [INFO] All tests passed!
    )
)

echo.
echo ========================================
echo  Memulai Binance Signal Generator Pro
echo  Real-time Trading Signals
echo  AI-Powered Technical Analysis
echo ========================================
echo.

echo [INFO] Starting application...
echo [INFO] Web interface akan tersedia di: http://localhost:5001
echo [INFO] Gunakan Ctrl+C untuk menghentikan aplikasi
echo [INFO] Jangan tutup window ini selama aplikasi berjalan
echo.

REM Start the application
python main.py

echo.
echo [INFO] Binance Signal Generator Pro telah berhenti.
echo [INFO] Terima kasih telah menggunakan aplikasi ini!
echo.
pause
