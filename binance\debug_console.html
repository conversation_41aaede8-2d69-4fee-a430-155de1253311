<!DOCTYPE html>
<html>
<head>
    <title>Debug Console</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 5px 0; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
    </style>
</head>
<body>
    <h1>Debug Console for Manual Analysis</h1>
    <div id="console-output"></div>
    
    <script>
        const output = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }
        
        // Test basic functionality
        log('Debug console loaded', 'info');
        
        // Test if elements exist
        setTimeout(() => {
            const btn = document.getElementById('start-analysis-btn');
            const statusText = document.querySelector('.status-text');
            const statusIndicator = document.getElementById('analysis-status-indicator');
            
            log(`Button exists: ${!!btn}`, btn ? 'info' : 'error');
            log(`Status text exists: ${!!statusText}`, statusText ? 'info' : 'error');
            log(`Status indicator exists: ${!!statusIndicator}`, statusIndicator ? 'info' : 'error');
            
            if (btn) {
                log(`Button innerHTML: ${btn.innerHTML}`, 'info');
                log(`Button disabled: ${btn.disabled}`, 'info');
                log(`Button className: ${btn.className}`, 'info');
            }
            
            if (statusText) {
                log(`Status text content: ${statusText.textContent}`, 'info');
            }
            
            if (statusIndicator) {
                log(`Status indicator className: ${statusIndicator.className}`, 'info');
            }
            
            // Test API
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    log(`API Status: ${JSON.stringify(data.success)}`, 'info');
                    log(`Analysis Status: ${JSON.stringify(data.analysis_status)}`, 'info');
                })
                .catch(error => {
                    log(`API Error: ${error.message}`, 'error');
                });
                
        }, 2000);
        
        // Test manual analysis
        function testManualAnalysis() {
            log('Testing manual analysis...', 'info');
            
            fetch('/api/start-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                log(`Manual analysis result: ${JSON.stringify(data)}`, 'info');
            })
            .catch(error => {
                log(`Manual analysis error: ${error.message}`, 'error');
            });
        }
        
        // Add test button
        const testBtn = document.createElement('button');
        testBtn.textContent = 'Test Manual Analysis';
        testBtn.onclick = testManualAnalysis;
        testBtn.style.cssText = 'margin: 10px; padding: 10px; background: #333; color: #0f0; border: 1px solid #0f0;';
        document.body.appendChild(testBtn);
        
    </script>
</body>
</html>
