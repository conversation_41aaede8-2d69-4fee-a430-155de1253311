#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CryptoArb Pro - Deteksi Peluang Arbitrase Cryptocurrency
Binance vs Bybit Real-time Arbitrage Detection System

Author: BOBACHEESE
Version: 1.0.0
Framework: Flask + HTML/CSS/JavaScript
Target: 300+ trading pairs, <2GB memory, 20+ tokens/second
"""

import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
import requests
from flask import Flask, render_template_string, jsonify, request
import concurrent.futures
from dataclasses import dataclass

# Konfigurasi Logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('arbitrage.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ArbitrageOpportunity:
    """Data class untuk peluang arbitrase dengan validasi kompleks dan DEX support"""
    symbol: str
    binance_price: float
    bybit_price: float
    profit_percentage: float
    volume_24h: float
    binance_volume: float
    bybit_volume: float
    direction: str  # 'binance_to_bybit', 'bybit_to_binance', 'cex_to_dex', 'dex_to_cex'
    timestamp: datetime
    networks: List[str]
    trading_fee: float = 0.1  # 0.1% per transaksi

    # Enhanced validation fields
    reliability_score: float = 0.0  # 0-100 score
    slippage_estimate: float = 0.0  # Estimated slippage %
    market_volatility: float = 0.0  # Volatility in last hour
    liquidity_depth: float = 0.0  # Order book depth
    execution_feasibility: bool = False  # Can be executed
    price_trend: str = "stable"  # "rising", "falling", "stable"
    network_congestion: Dict[str, str] = None  # Network status

    # DEX-specific fields (all optional untuk backward compatibility)
    dex_name: Optional[str] = None  # "Raydium", "Orca", "Jupiter", "Serum"
    dex_pair_address: Optional[str] = None  # Solana pair address
    dex_liquidity_usd: Optional[float] = None  # USD liquidity di DEX
    token_mint_address: Optional[str] = None  # SPL token mint address
    dex_volume_24h: Optional[float] = None  # 24h volume di DEX
    price_impact_estimate: Optional[float] = None  # Estimated price impact %
    dex_link: Optional[str] = None  # DexScreener pair URL
    arbitrage_type: str = "CEX-CEX"  # "CEX-CEX" atau "DEX-CEX"
    gas_fee_estimate: Optional[float] = None  # Estimated Solana transaction fee in USD
    dex_last_trade_time: Optional[datetime] = None  # Last trade timestamp
    dex_trade_count_24h: Optional[int] = None  # Number of trades in 24h

    def __post_init__(self):
        if self.network_congestion is None:
            self.network_congestion = {}

    @property
    def net_profit(self) -> float:
        """Profit bersih setelah trading fee, slippage, dan gas fees"""
        gross_profit = self.profit_percentage
        total_fees = (self.trading_fee * 2) + self.slippage_estimate

        # Add gas fees for DEX-CEX arbitrage
        if self.arbitrage_type == "DEX-CEX" and self.gas_fee_estimate:
            # Convert gas fee to percentage (assuming $1000 trade size)
            gas_fee_percentage = (self.gas_fee_estimate / 1000) * 100
            total_fees += gas_fee_percentage

        return max(0, gross_profit - total_fees)

    @property
    def risk_level(self) -> str:
        """Tingkat risiko berdasarkan reliability score dan arbitrage type"""
        base_score = self.reliability_score

        # Adjust for DEX-CEX (higher risk)
        if self.arbitrage_type == "DEX-CEX":
            base_score -= 10  # Reduce score by 10 for DEX complexity

        if base_score >= 80:
            return "Rendah"
        elif base_score >= 60:
            return "Sedang"
        elif base_score >= 40:
            return "Tinggi"
        else:
            return "Sangat Tinggi"

    @property
    def quality_grade(self) -> str:
        """Grade kualitas peluang A-F dengan DEX considerations"""
        score = self.reliability_score
        profit = self.net_profit

        # Adjust thresholds for DEX-CEX (stricter requirements)
        if self.arbitrage_type == "DEX-CEX":
            if score >= 85 and profit >= 3.0:
                return "A+"
            elif score >= 75 and profit >= 2.0:
                return "A"
            elif score >= 65 and profit >= 1.5:
                return "B"
            elif score >= 55 and profit >= 1.0:
                return "C"
            elif score >= 45:
                return "D"
            else:
                return "F"
        else:
            # Original CEX-CEX grading
            if score >= 90 and profit >= 2.0:
                return "A+"
            elif score >= 80 and profit >= 1.5:
                return "A"
            elif score >= 70 and profit >= 1.0:
                return "B"
            elif score >= 60 and profit >= 0.5:
                return "C"
            elif score >= 50:
                return "D"
            else:
                return "F"

    @property
    def is_dex_arbitrage(self) -> bool:
        """Check if this is a DEX-CEX arbitrage opportunity"""
        return self.arbitrage_type == "DEX-CEX"

    @property
    def effective_liquidity(self) -> float:
        """Get effective liquidity (minimum of CEX and DEX for DEX-CEX)"""
        if self.arbitrage_type == "DEX-CEX" and self.dex_liquidity_usd:
            cex_liquidity = min(self.binance_volume, self.bybit_volume)
            return min(cex_liquidity, self.dex_liquidity_usd)
        else:
            return min(self.binance_volume, self.bybit_volume)

class ValidationEngine:
    """Engine untuk validasi kompleks peluang arbitrase"""

    def __init__(self):
        self.blacklisted_pairs = set()
        self.price_history = {}  # symbol -> list of prices
        self.volatility_cache = {}
        self.network_status = {
            'ETH': 'normal',
            'BSC': 'normal',
            'TRC20': 'normal',
            'POLYGON': 'normal',
            'ARBITRUM': 'normal'
        }

    def add_to_blacklist(self, symbol: str, reason: str = "false_positive"):
        """Tambahkan symbol ke blacklist"""
        self.blacklisted_pairs.add(symbol)
        logger.warning(f"[BLACKLIST] Added {symbol} to blacklist: {reason}")

    def is_blacklisted(self, symbol: str) -> bool:
        """Cek apakah symbol dalam blacklist"""
        return symbol in self.blacklisted_pairs

    def update_price_history(self, symbol: str, price: float):
        """Update riwayat harga untuk analisis trend"""
        if symbol not in self.price_history:
            self.price_history[symbol] = []

        self.price_history[symbol].append({
            'price': price,
            'timestamp': time.time()
        })

        # Keep only last 60 data points (30 minutes with 30s interval)
        if len(self.price_history[symbol]) > 60:
            self.price_history[symbol] = self.price_history[symbol][-60:]

    def calculate_volatility(self, symbol: str) -> float:
        """Hitung volatilitas dalam 1 jam terakhir"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
            return 0.0

        prices = [p['price'] for p in self.price_history[symbol]]
        if len(prices) < 2:
            return 0.0

        # Calculate standard deviation
        mean_price = sum(prices) / len(prices)
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        volatility = (variance ** 0.5) / mean_price * 100  # Percentage volatility

        self.volatility_cache[symbol] = volatility
        return volatility

    def analyze_price_trend(self, symbol: str) -> str:
        """Analisis trend harga: rising, falling, stable"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < 5:
            return "stable"

        recent_prices = [p['price'] for p in self.price_history[symbol][-5:]]

        # Simple trend analysis
        increases = 0
        decreases = 0

        for i in range(1, len(recent_prices)):
            if recent_prices[i] > recent_prices[i-1]:
                increases += 1
            elif recent_prices[i] < recent_prices[i-1]:
                decreases += 1

        if increases >= 3:
            return "rising"
        elif decreases >= 3:
            return "falling"
        else:
            return "stable"

    def estimate_slippage(self, symbol: str, volume_24h: float, profit_pct: float) -> float:
        """Estimasi slippage berdasarkan volume dan profit"""
        # Base slippage estimation
        base_slippage = 0.1  # 0.1% base

        # Volume factor (lower volume = higher slippage)
        if volume_24h < 50000:
            volume_factor = 0.5
        elif volume_24h < 100000:
            volume_factor = 0.3
        elif volume_24h < 500000:
            volume_factor = 0.2
        else:
            volume_factor = 0.1

        # Profit factor (higher profit might indicate wider spread)
        profit_factor = min(profit_pct * 0.1, 0.3)

        # Volatility factor
        volatility = self.volatility_cache.get(symbol, 0)
        volatility_factor = min(volatility * 0.05, 0.2)

        total_slippage = base_slippage + volume_factor + profit_factor + volatility_factor
        return min(total_slippage, 2.0)  # Cap at 2%

    def calculate_reliability_score(self, opportunity: ArbitrageOpportunity) -> float:
        """Hitung reliability score 0-100"""
        score = 100.0

        # Blacklist check
        if self.is_blacklisted(opportunity.symbol):
            return 0.0

        # Volume score (30 points max)
        volume_score = min(opportunity.volume_24h / 100000 * 30, 30)

        # Profit consistency score (25 points max)
        profit_score = min(opportunity.profit_percentage * 5, 25)

        # Volatility score (20 points max) - lower volatility is better
        volatility = self.volatility_cache.get(opportunity.symbol, 0)
        volatility_score = max(20 - volatility * 2, 0)

        # Network availability score (15 points max)
        network_score = len([n for n in opportunity.networks
                           if self.network_status.get(n, 'congested') == 'normal']) * 3
        network_score = min(network_score, 15)

        # Price trend score (10 points max)
        trend = self.analyze_price_trend(opportunity.symbol)
        if trend == "stable":
            trend_score = 10
        elif trend in ["rising", "falling"]:
            trend_score = 7
        else:
            trend_score = 5

        final_score = volume_score + profit_score + volatility_score + network_score + trend_score
        return min(final_score, 100.0)

    def validate_opportunity(self, opportunity: ArbitrageOpportunity) -> ArbitrageOpportunity:
        """Validasi komprehensif peluang arbitrase"""
        # Update price history
        avg_price = (opportunity.binance_price + opportunity.bybit_price) / 2
        self.update_price_history(opportunity.symbol, avg_price)

        # Calculate enhanced metrics
        opportunity.market_volatility = self.calculate_volatility(opportunity.symbol)
        opportunity.slippage_estimate = self.estimate_slippage(
            opportunity.symbol, opportunity.volume_24h, opportunity.profit_percentage
        )
        opportunity.reliability_score = self.calculate_reliability_score(opportunity)
        opportunity.price_trend = self.analyze_price_trend(opportunity.symbol)

        # Liquidity depth estimation
        opportunity.liquidity_depth = min(opportunity.binance_volume, opportunity.bybit_volume)

        # Execution feasibility
        min_executable_profit = 0.5 + opportunity.slippage_estimate
        opportunity.execution_feasibility = (
            opportunity.net_profit >= min_executable_profit and
            opportunity.reliability_score >= 40 and
            not self.is_blacklisted(opportunity.symbol)
        )

        # Network congestion status
        opportunity.network_congestion = {
            network: self.network_status.get(network, 'unknown')
            for network in opportunity.networks
        }

        return opportunity

    def validate_dex_opportunity(self, opportunity: ArbitrageOpportunity) -> ArbitrageOpportunity:
        """Validasi khusus untuk DEX-CEX arbitrage"""
        try:
            if opportunity.arbitrage_type != "DEX-CEX":
                return opportunity

            # Enhanced DEX-specific validation
            if opportunity.dex_liquidity_usd and opportunity.dex_liquidity_usd < 5000:
                opportunity.reliability_score *= 0.7  # Reduce score for low DEX liquidity

            # Price impact validation
            if opportunity.price_impact_estimate and opportunity.price_impact_estimate > 2.0:
                opportunity.execution_feasibility = False
                opportunity.reliability_score *= 0.5

            # DEX trade activity validation
            if opportunity.dex_trade_count_24h and opportunity.dex_trade_count_24h < 10:
                opportunity.reliability_score *= 0.8  # Reduce score for low activity

            # Last trade time validation
            if opportunity.dex_last_trade_time:
                time_diff = datetime.now() - opportunity.dex_last_trade_time
                if time_diff.total_seconds() > 3600:  # More than 1 hour
                    opportunity.reliability_score *= 0.6

            # Gas fee impact on profitability
            if opportunity.gas_fee_estimate:
                # If gas fee is more than 20% of expected profit, reduce feasibility
                expected_profit_usd = 1000 * (opportunity.profit_percentage / 100)  # Assume $1000 trade
                if opportunity.gas_fee_estimate > expected_profit_usd * 0.2:
                    opportunity.execution_feasibility = False

            return opportunity

        except Exception as e:
            logger.error(f"[DEX-ERROR] DEX opportunity validation failed: {e}")
            return opportunity

    def calculate_dex_price_impact(self, trade_amount: float, liquidity: float) -> float:
        """Calculate price impact untuk DEX trade"""
        try:
            if liquidity <= 0:
                return 10.0  # Maximum impact if no liquidity data

            # Simple price impact model: impact = (trade_amount / liquidity) * 100
            # This is a simplified model; real DEX price impact is more complex
            impact = (trade_amount / liquidity) * 100

            # Cap at 10% maximum impact
            return min(impact, 10.0)

        except Exception as e:
            logger.error(f"[DEX-ERROR] Price impact calculation failed: {e}")
            return 5.0  # Conservative fallback

    def estimate_solana_gas_fees(self) -> float:
        """Estimate Solana gas fees in USD"""
        try:
            # Base transaction fee: ~0.000005 SOL
            base_fee = 0.000005

            # Priority fee (dynamic, conservative estimate)
            priority_fee = 0.000001

            total_sol = base_fee + priority_fee

            # Convert to USD (should be updated with real-time SOL price)
            sol_price_usd = 95.0  # Conservative estimate

            return total_sol * sol_price_usd

        except Exception as e:
            logger.error(f"[DEX-ERROR] Gas fee estimation failed: {e}")
            return 0.01  # $0.01 fallback

    def validate_dex_pair_status(self, pair_data: Dict) -> bool:
        """Validate DEX pair status dan activity"""
        try:
            if not isinstance(pair_data, dict):
                return False

            # Check required fields
            required_fields = ['pairAddress', 'baseToken', 'quoteToken', 'priceUsd']
            for field in required_fields:
                if field not in pair_data:
                    return False

            # Check if pair is active (has recent trades)
            if 'volume' in pair_data:
                volume_24h = float(pair_data['volume'].get('h24', 0))
                if volume_24h < 1000:  # Less than $1000 volume
                    return False

            # Check liquidity
            if 'liquidity' in pair_data:
                liquidity_usd = float(pair_data['liquidity'].get('usd', 0))
                if liquidity_usd < 5000:  # Less than $5000 liquidity
                    return False

            # Check price data freshness
            if 'priceChange' in pair_data:
                price_change = pair_data['priceChange']
                if 'h24' not in price_change:  # No 24h price change data
                    return False

            return True

        except Exception as e:
            logger.error(f"[DEX-ERROR] DEX pair status validation failed: {e}")
            return False

class DexScreenerClient:
    """Client untuk DexScreener API dengan rate limiting dan caching"""

    def __init__(self):
        self.base_url = "https://api.dexscreener.com/latest/dex"
        self.rate_limiter = RateLimiter(120, 60)  # 120 requests per minute
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CryptoArb-Pro-DEX/2.0',
            'Content-Type': 'application/json'
        })
        self.cache = {}
        self.cache_expiry = 60  # 60 seconds cache
        self.last_health_check = 0
        self.is_healthy = False

        # Backup endpoints
        self.backup_endpoints = [
            "https://api.dexscreener.com/latest/dex",
            "https://api.dexscreener.com/latest/dex"  # Same for now, can add mirrors
        ]

        # Error tracking
        self.error_count = 0
        self.success_count = 0

    def health_check(self) -> bool:
        """Cek status kesehatan DexScreener API"""
        try:
            if time.time() - self.last_health_check < 30:  # Cache for 30s
                return self.is_healthy

            # Simple health check dengan search endpoint
            response = self.session.get(f"{self.base_url}/search/?q=SOL", timeout=5)
            self.is_healthy = response.status_code == 200
            self.last_health_check = time.time()

            if self.is_healthy:
                self.success_count += 1
            else:
                self.error_count += 1

            return self.is_healthy

        except Exception as e:
            logger.error(f"[DEX-ERROR] DexScreener health check failed: {e}")
            self.is_healthy = False
            self.error_count += 1
            return False

    def _make_request_with_retry(self, url: str, max_retries: int = 3) -> Optional[Dict]:
        """Make request dengan exponential backoff retry"""
        for attempt in range(max_retries):
            try:
                if not self.rate_limiter.can_make_call():
                    wait_time = self.rate_limiter.wait_time()
                    if wait_time > 0:
                        time.sleep(wait_time)

                response = self.session.get(url, timeout=10)

                if response.status_code == 200:
                    self.success_count += 1
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"[DEX-WARN] Rate limited, waiting {wait_time}s")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.warning(f"[DEX-WARN] API returned status {response.status_code}")
                    self.error_count += 1
                    return None

            except requests.exceptions.Timeout:
                logger.warning(f"[DEX-WARN] Request timeout, attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                continue
            except Exception as e:
                logger.error(f"[DEX-ERROR] Request failed: {e}")
                self.error_count += 1
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                continue

        return None

    def _get_cache_key(self, endpoint: str, params: str = "") -> str:
        """Generate cache key"""
        return f"dex_{endpoint}_{params}_{int(time.time() // self.cache_expiry)}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache is still valid"""
        if cache_key not in self.cache:
            return False

        cached_time = self.cache[cache_key].get('timestamp', 0)
        return time.time() - cached_time < self.cache_expiry

    def search_token(self, symbol: str) -> Optional[Dict]:
        """Search token by symbol dengan validation dan caching"""
        try:
            if not symbol or len(symbol) < 2:
                return None

            # Normalize symbol
            symbol = symbol.upper().strip()
            cache_key = self._get_cache_key("search", symbol)

            # Check cache first
            if self._is_cache_valid(cache_key):
                logger.info(f"[DEX-INFO] Cache hit for search: {symbol}")
                return self.cache[cache_key]['data']

            # Make API request
            url = f"{self.base_url}/search/?q={symbol}"
            response_data = self._make_request_with_retry(url)

            if response_data and self.validate_response(response_data):
                # Cache the response
                self.cache[cache_key] = {
                    'data': response_data,
                    'timestamp': time.time()
                }

                logger.info(f"[DEX-INFO] Search successful for {symbol}: {len(response_data.get('pairs', []))} pairs found")
                return response_data

            return None

        except Exception as e:
            logger.error(f"[DEX-ERROR] Search token failed for {symbol}: {e}")
            return None

    def get_pairs(self, token_address: str) -> Optional[List[Dict]]:
        """Get trading pairs by token address"""
        try:
            if not token_address or len(token_address) < 32:
                return None

            cache_key = self._get_cache_key("pairs", token_address)

            # Check cache first
            if self._is_cache_valid(cache_key):
                logger.info(f"[DEX-INFO] Cache hit for pairs: {token_address[:8]}...")
                return self.cache[cache_key]['data']

            # Make API request
            url = f"{self.base_url}/tokens/{token_address}"
            response_data = self._make_request_with_retry(url)

            if response_data and 'pairs' in response_data:
                pairs = response_data['pairs']

                # Cache the response
                self.cache[cache_key] = {
                    'data': pairs,
                    'timestamp': time.time()
                }

                logger.info(f"[DEX-INFO] Pairs retrieved for {token_address[:8]}...: {len(pairs)} pairs")
                return pairs

            return None

        except Exception as e:
            logger.error(f"[DEX-ERROR] Get pairs failed for {token_address}: {e}")
            return None

    def get_pair_info(self, pair_address: str) -> Optional[Dict]:
        """Get detailed pair information dengan caching"""
        try:
            if not pair_address:
                return None

            cache_key = self._get_cache_key("pair_info", pair_address)

            # Check cache first
            if self._is_cache_valid(cache_key):
                logger.info(f"[DEX-INFO] Cache hit for pair info: {pair_address[:8]}...")
                return self.cache[cache_key]['data']

            # Make API request
            url = f"{self.base_url}/pairs/{pair_address}"
            response_data = self._make_request_with_retry(url)

            if response_data and 'pair' in response_data:
                pair_info = response_data['pair']

                # Cache the response
                self.cache[cache_key] = {
                    'data': pair_info,
                    'timestamp': time.time()
                }

                logger.info(f"[DEX-INFO] Pair info retrieved for {pair_address[:8]}...")
                return pair_info

            return None

        except Exception as e:
            logger.error(f"[DEX-ERROR] Get pair info failed for {pair_address}: {e}")
            return None

    def validate_response(self, response: Dict) -> bool:
        """Validate DexScreener response structure"""
        try:
            if not isinstance(response, dict):
                return False

            # For search responses
            if 'pairs' in response:
                pairs = response['pairs']
                if not isinstance(pairs, list):
                    return False

                # Validate at least one pair structure
                if pairs:
                    sample_pair = pairs[0]
                    required_fields = ['chainId', 'dexId', 'pairAddress', 'baseToken', 'quoteToken', 'priceUsd']

                    for field in required_fields:
                        if field not in sample_pair:
                            logger.warning(f"[DEX-WARN] Missing required field: {field}")
                            return False

                    # Validate token structure
                    for token_key in ['baseToken', 'quoteToken']:
                        token = sample_pair[token_key]
                        if not isinstance(token, dict) or 'address' not in token or 'symbol' not in token:
                            logger.warning(f"[DEX-WARN] Invalid token structure: {token_key}")
                            return False

                return True

            # For single pair responses
            if 'pair' in response:
                pair = response['pair']
                return isinstance(pair, dict) and 'pairAddress' in pair

            return True

        except Exception as e:
            logger.error(f"[DEX-ERROR] Response validation failed: {e}")
            return False

    def get_success_rate(self) -> float:
        """Get API success rate percentage"""
        total_calls = self.success_count + self.error_count
        if total_calls == 0:
            return 100.0
        return (self.success_count / total_calls) * 100

    def reset_stats(self):
        """Reset API call statistics"""
        self.success_count = 0
        self.error_count = 0

    def cleanup_cache(self):
        """Cleanup expired cache entries"""
        current_time = time.time()
        expired_keys = []

        for key, data in self.cache.items():
            if current_time - data['timestamp'] > self.cache_expiry:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        if expired_keys:
            logger.info(f"[DEX-INFO] Cleaned up {len(expired_keys)} expired cache entries")

class SolanaValidator:
    """Validator untuk Solana-specific data dan addresses"""

    def __init__(self):
        # Known Solana DEX program IDs
        self.dex_program_ids = {
            'raydium': '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
            'orca': '9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP',
            'serum': '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin',
            'jupiter': 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'
        }

        # DEX priority ranking (higher = better)
        self.dex_priorities = {
            'raydium': 4,
            'orca': 3,
            'jupiter': 2,
            'serum': 1
        }

        # Base58 alphabet for Solana addresses
        self.base58_alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

    def validate_mint_address(self, address: str) -> bool:
        """Validate Solana mint address format"""
        try:
            if not address or not isinstance(address, str):
                return False

            # Check length (32-44 characters for base58 encoded 32-byte address)
            if not (32 <= len(address) <= 44):
                return False

            # Check base58 encoding
            for char in address:
                if char not in self.base58_alphabet:
                    return False

            # Additional validation: should not start with certain characters
            if address[0] in '0OIl':
                return False

            return True

        except Exception as e:
            logger.error(f"[DEX-ERROR] Mint address validation failed: {e}")
            return False

    def validate_program_id(self, program_id: str) -> bool:
        """Validate Solana program ID"""
        try:
            if not program_id:
                return False

            # Check if it's a known DEX program ID
            if program_id in self.dex_program_ids.values():
                return True

            # Otherwise validate as regular Solana address
            return self.validate_mint_address(program_id)

        except Exception as e:
            logger.error(f"[DEX-ERROR] Program ID validation failed: {e}")
            return False

    def validate_token_metadata(self, metadata: Dict) -> bool:
        """Validate token metadata consistency"""
        try:
            if not isinstance(metadata, dict):
                return False

            required_fields = ['address', 'symbol', 'name']
            for field in required_fields:
                if field not in metadata or not metadata[field]:
                    return False

            # Validate address format
            if not self.validate_mint_address(metadata['address']):
                return False

            # Validate symbol (2-10 characters, alphanumeric)
            symbol = metadata['symbol']
            if not (2 <= len(symbol) <= 10) or not symbol.replace('$', '').isalnum():
                return False

            # Validate decimals if present
            if 'decimals' in metadata:
                decimals = metadata['decimals']
                if not isinstance(decimals, int) or not (0 <= decimals <= 9):
                    return False

            return True

        except Exception as e:
            logger.error(f"[DEX-ERROR] Token metadata validation failed: {e}")
            return False

    def get_dex_priority(self, dex_name: str) -> int:
        """Get DEX priority ranking"""
        if not dex_name:
            return 0

        dex_name_lower = dex_name.lower()
        return self.dex_priorities.get(dex_name_lower, 0)

    def is_solana_chain(self, chain_id: str) -> bool:
        """Check if chain ID is Solana"""
        return chain_id and chain_id.lower() == 'solana'

    def normalize_dex_name(self, dex_id: str) -> str:
        """Normalize DEX name from DexScreener dexId"""
        if not dex_id:
            return "unknown"

        dex_id_lower = dex_id.lower()

        # Map common DexScreener dexIds to normalized names
        dex_mapping = {
            'raydium': 'raydium',
            'orca': 'orca',
            'jupiter': 'jupiter',
            'serum': 'serum',
            'aldrin': 'aldrin',
            'saber': 'saber',
            'mercurial': 'mercurial'
        }

        return dex_mapping.get(dex_id_lower, dex_id_lower)

    def estimate_solana_gas_fee(self) -> float:
        """Estimate Solana transaction fee in USD"""
        try:
            # Base Solana transaction fee: ~0.000005 SOL
            base_fee_sol = 0.000005

            # Priority fee (dynamic, using conservative estimate)
            priority_fee_sol = 0.000001

            total_fee_sol = base_fee_sol + priority_fee_sol

            # Convert to USD (using approximate SOL price, can be enhanced with real-time data)
            sol_price_usd = 95.0  # Conservative estimate, should be updated with real price

            return total_fee_sol * sol_price_usd

        except Exception as e:
            logger.error(f"[DEX-ERROR] Gas fee estimation failed: {e}")
            return 0.01  # Fallback to $0.01

class RateLimiter:
    """Rate limiter untuk API calls"""
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def can_make_call(self) -> bool:
        with self.lock:
            now = time.time()
            # Hapus calls yang sudah expired
            self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
            
            if len(self.calls) < self.max_calls:
                self.calls.append(now)
                return True
            return False
    
    def wait_time(self) -> float:
        """Waktu tunggu sebelum bisa melakukan call berikutnya"""
        with self.lock:
            if len(self.calls) < self.max_calls:
                return 0
            oldest_call = min(self.calls)
            return max(0, self.time_window - (time.time() - oldest_call))

class BinanceClient:
    """Client untuk Binance API"""
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.rate_limiter = RateLimiter(1200, 60)  # 1200 requests per minute
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        })
        self.last_health_check = 0
        self.is_healthy = False
    
    def health_check(self) -> bool:
        """Cek status kesehatan API"""
        try:
            if time.time() - self.last_health_check < 10:
                return self.is_healthy
            
            response = self.session.get(f"{self.base_url}/ping", timeout=5)
            self.is_healthy = response.status_code == 200
            self.last_health_check = time.time()
            return self.is_healthy
        except Exception as e:
            logger.error(f"Binance health check failed: {e}")
            self.is_healthy = False
            return False
    
    def get_24hr_ticker(self) -> Optional[List[Dict]]:
        """Ambil data ticker 24 jam"""
        if not self.rate_limiter.can_make_call():
            wait_time = self.rate_limiter.wait_time()
            if wait_time > 0:
                time.sleep(wait_time)
        
        try:
            response = self.session.get(f"{self.base_url}/ticker/24hr", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Binance API error: {e}")
            return None
    
    def get_exchange_info(self) -> Optional[Dict]:
        """Ambil informasi exchange"""
        try:
            response = self.session.get(f"{self.base_url}/exchangeInfo", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Binance exchange info error: {e}")
            return None

class BybitClient:
    """Client untuk Bybit API"""
    def __init__(self):
        self.base_url = "https://api.bybit.com/v5"
        self.rate_limiter = RateLimiter(120, 60)  # 120 requests per minute
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        })
        self.last_health_check = 0
        self.is_healthy = False
    
    def health_check(self) -> bool:
        """Cek status kesehatan API"""
        try:
            if time.time() - self.last_health_check < 10:
                return self.is_healthy
            
            response = self.session.get(f"{self.base_url}/market/time", timeout=5)
            self.is_healthy = response.status_code == 200
            self.last_health_check = time.time()
            return self.is_healthy
        except Exception as e:
            logger.error(f"Bybit health check failed: {e}")
            self.is_healthy = False
            return False
    
    def get_tickers(self) -> Optional[Dict]:
        """Ambil data ticker spot market"""
        if not self.rate_limiter.can_make_call():
            wait_time = self.rate_limiter.wait_time()
            if wait_time > 0:
                time.sleep(wait_time)
        
        try:
            params = {'category': 'spot'}
            response = self.session.get(f"{self.base_url}/market/tickers", params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Bybit API error: {e}")
            return None

class ArbitrageEngine:
    """Engine utama untuk deteksi arbitrase dengan validasi kompleks"""
    def __init__(self):
        self.binance_client = BinanceClient()
        self.bybit_client = BybitClient()
        self.validation_engine = ValidationEngine()
        self.opportunities = []
        self.last_update = None
        self.stats = {
            'total_scanned': 0,
            'opportunities_found': 0,
            'average_profit': 0,
            'scan_duration': 0,
            'high_quality_count': 0,
            'executable_count': 0,
            'average_reliability': 0
        }

        # Target symbols prioritas dengan kategori
        self.priority_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
            'DOGEUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'DOTUSDT',
            'LINKUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'LTCUSDT',
            'TRXUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT',
            'SHIBUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'BONKUSDT', 'WIFUSDT'
        ]

        # Cache untuk data dengan timestamp
        self.binance_cache = {}
        self.bybit_cache = {}
        self.cache_expiry = 30  # 30 detik

        # Cross-validation endpoints
        self.backup_endpoints = {
            'binance': [
                'https://api.binance.com/api/v3/ticker/24hr',
                'https://api1.binance.com/api/v3/ticker/24hr',
                'https://api2.binance.com/api/v3/ticker/24hr'
            ],
            'bybit': [
                'https://api.bybit.com/v5/market/tickers',
                'https://api.bytick.com/v5/market/tickers'
            ]
        }

        # Performance monitoring
        self.scan_performance = []
        self.error_counts = {'binance': 0, 'bybit': 0}
        self.success_rates = {'binance': 100.0, 'bybit': 100.0}
    
    def get_common_symbols(self, binance_data: List[Dict], bybit_data: Dict) -> List[str]:
        """Dapatkan symbol yang tersedia di kedua bursa"""
        binance_symbols = {item['symbol'] for item in binance_data}
        bybit_symbols = {item['symbol'] for item in bybit_data.get('result', {}).get('list', [])}
        
        common = binance_symbols.intersection(bybit_symbols)
        
        # Prioritaskan symbol yang ada di priority list
        priority_common = [s for s in self.priority_symbols if s in common]
        other_common = [s for s in common if s not in self.priority_symbols]
        
        return priority_common + other_common[:280]  # Total 300 symbols max
    
    def calculate_arbitrage(self, symbol: str, binance_price: float, bybit_price: float,
                          binance_volume: float, bybit_volume: float) -> Optional[ArbitrageOpportunity]:
        """Hitung peluang arbitrase"""
        if binance_price <= 0 or bybit_price <= 0:
            return None
        
        # Minimum volume requirement ($10,000)
        min_volume = 10000
        if binance_volume < min_volume or bybit_volume < min_volume:
            return None
        
        # Hitung profit percentage
        if binance_price > bybit_price:
            profit_pct = ((binance_price - bybit_price) / bybit_price) * 100
            direction = 'bybit_to_binance'
        else:
            profit_pct = ((bybit_price - binance_price) / binance_price) * 100
            direction = 'binance_to_bybit'
        
        # Filter profit range (0.5% - 200%)
        if profit_pct < 0.5 or profit_pct > 200:
            return None
        
        # Networks yang umum didukung
        networks = ['ETH', 'BSC', 'TRC20', 'POLYGON', 'ARBITRUM']
        
        return ArbitrageOpportunity(
            symbol=symbol,
            binance_price=binance_price,
            bybit_price=bybit_price,
            profit_percentage=profit_pct,
            volume_24h=min(binance_volume, bybit_volume),
            binance_volume=binance_volume,
            bybit_volume=bybit_volume,
            direction=direction,
            timestamp=datetime.now(),
            networks=networks
        )

    def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """Scan peluang arbitrase dengan validasi kompleks"""
        start_time = time.time()
        opportunities = []

        try:
            # Multi-source data fetching dengan fallback
            binance_data = self._fetch_with_fallback('binance')
            bybit_data = self._fetch_with_fallback('bybit')

            if not binance_data or not bybit_data:
                logger.warning("Failed to fetch data from one or both exchanges")
                self._update_error_rates()
                return opportunities

            # Cross-validation data consistency
            if not self._validate_data_consistency(binance_data, bybit_data):
                logger.warning("Data consistency check failed")
                return opportunities

            # Konversi data untuk processing
            binance_dict = {item['symbol']: item for item in binance_data}
            bybit_dict = {item['symbol']: item for item in bybit_data.get('result', {}).get('list', [])}

            common_symbols = self.get_common_symbols(binance_data, bybit_data)
            self.stats['total_scanned'] = len(common_symbols)

            # Parallel processing dengan enhanced validation
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = []

                for symbol in common_symbols:
                    if symbol in binance_dict and symbol in bybit_dict:
                        future = executor.submit(
                            self._process_symbol_enhanced,
                            symbol,
                            binance_dict[symbol],
                            bybit_dict[symbol]
                        )
                        futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    try:
                        opportunity = future.result()
                        if opportunity and opportunity.execution_feasibility:
                            opportunities.append(opportunity)
                    except Exception as e:
                        logger.error(f"Error processing symbol: {e}")

            # Advanced sorting dengan multiple criteria
            opportunities.sort(key=lambda x: (
                x.reliability_score,
                x.net_profit,
                -x.slippage_estimate
            ), reverse=True)

            # Enhanced statistics
            self._update_enhanced_stats(opportunities, start_time)

            # Performance monitoring
            self._track_scan_performance(len(opportunities), time.time() - start_time)

            logger.info(f"Enhanced scan completed: {len(opportunities)} validated opportunities "
                       f"found in {self.stats['scan_duration']:.2f}s")

        except Exception as e:
            logger.error(f"Enhanced scan error: {e}")
            self._update_error_rates()

        return opportunities

    def _fetch_with_fallback(self, exchange: str) -> Optional[List]:
        """Fetch data dengan multiple endpoint fallback"""
        if exchange == 'binance':
            data = self.binance_client.get_24hr_ticker()
            if data:
                self.error_counts['binance'] = max(0, self.error_counts['binance'] - 1)
                return data
            else:
                self.error_counts['binance'] += 1
                # Try backup endpoints if available
                return self._try_backup_endpoints('binance')

        elif exchange == 'bybit':
            data = self.bybit_client.get_tickers()
            if data:
                self.error_counts['bybit'] = max(0, self.error_counts['bybit'] - 1)
                return data
            else:
                self.error_counts['bybit'] += 1
                return self._try_backup_endpoints('bybit')

        return None

    def _try_backup_endpoints(self, exchange: str) -> Optional[List]:
        """Coba endpoint backup jika primary gagal"""
        # Placeholder for backup endpoint logic
        # In production, implement actual backup endpoint calls
        logger.warning(f"Primary {exchange} endpoint failed, backup not implemented")
        return None

    def _validate_data_consistency(self, binance_data: List, bybit_data: Dict) -> bool:
        """Validasi konsistensi data antar exchange"""
        try:
            # Check data freshness
            current_time = time.time()

            # Basic validation
            if not binance_data or not bybit_data.get('result', {}).get('list'):
                return False

            # Check if we have reasonable number of symbols
            binance_symbols = len(binance_data)
            bybit_symbols = len(bybit_data.get('result', {}).get('list', []))

            if binance_symbols < 50 or bybit_symbols < 50:
                logger.warning(f"Insufficient symbols: Binance={binance_symbols}, Bybit={bybit_symbols}")
                return False

            return True

        except Exception as e:
            logger.error(f"Data consistency validation error: {e}")
            return False

    def _process_symbol_enhanced(self, symbol: str, binance_item: Dict, bybit_item: Dict) -> Optional[ArbitrageOpportunity]:
        """Process single symbol dengan enhanced validation"""
        try:
            # Parse data
            binance_price = float(binance_item.get('lastPrice', 0))
            binance_volume = float(binance_item.get('quoteVolume', 0))
            bybit_price = float(bybit_item.get('lastPrice', 0))
            bybit_volume = float(bybit_item.get('turnover24h', 0))

            # Create basic opportunity
            opportunity = self.calculate_arbitrage(symbol, binance_price, bybit_price, binance_volume, bybit_volume)

            if opportunity:
                # Apply enhanced validation
                opportunity = self.validation_engine.validate_opportunity(opportunity)

                # Additional feasibility checks
                if self._additional_feasibility_check(opportunity):
                    return opportunity

            return None

        except Exception as e:
            logger.error(f"Enhanced processing error for {symbol}: {e}")
            return None

    def _additional_feasibility_check(self, opportunity: ArbitrageOpportunity) -> bool:
        """Pemeriksaan feasibility tambahan"""
        try:
            # Minimum executable amount check
            min_amount = 100  # $100 minimum
            estimated_amount = min_amount

            # Check if profit covers minimum trading requirements
            min_profit_usd = estimated_amount * (opportunity.net_profit / 100)

            if min_profit_usd < 1.0:  # Minimum $1 profit
                return False

            # Network availability check
            available_networks = [
                network for network, status in opportunity.network_congestion.items()
                if status == 'normal'
            ]

            if len(available_networks) == 0:
                return False

            # Volatility check
            if opportunity.market_volatility > 10.0:  # 10% volatility threshold
                return False

            return True

        except Exception as e:
            logger.error(f"Feasibility check error: {e}")
            return False

    def _update_enhanced_stats(self, opportunities: List[ArbitrageOpportunity], start_time: float):
        """Update enhanced statistics"""
        self.opportunities = opportunities
        self.last_update = datetime.now()
        self.stats['opportunities_found'] = len(opportunities)
        self.stats['scan_duration'] = time.time() - start_time

        if opportunities:
            self.stats['average_profit'] = sum(op.profit_percentage for op in opportunities) / len(opportunities)
            self.stats['average_reliability'] = sum(op.reliability_score for op in opportunities) / len(opportunities)
            self.stats['high_quality_count'] = len([op for op in opportunities if op.quality_grade in ['A+', 'A', 'B']])
            self.stats['executable_count'] = len([op for op in opportunities if op.execution_feasibility])
        else:
            self.stats['average_profit'] = 0
            self.stats['average_reliability'] = 0
            self.stats['high_quality_count'] = 0
            self.stats['executable_count'] = 0

    def _track_scan_performance(self, opportunities_found: int, duration: float):
        """Track scan performance untuk monitoring"""
        performance_data = {
            'timestamp': time.time(),
            'opportunities_found': opportunities_found,
            'duration': duration,
            'tokens_per_second': self.stats['total_scanned'] / duration if duration > 0 else 0
        }

        self.scan_performance.append(performance_data)

        # Keep only last 100 scans
        if len(self.scan_performance) > 100:
            self.scan_performance = self.scan_performance[-100:]

    def _update_error_rates(self):
        """Update error rates untuk monitoring"""
        total_attempts = 100  # Base untuk perhitungan rate

        self.success_rates['binance'] = max(0, 100 - (self.error_counts['binance'] / total_attempts * 100))
        self.success_rates['bybit'] = max(0, 100 - (self.error_counts['bybit'] / total_attempts * 100))

        # Reset error counts jika terlalu tinggi
        if self.error_counts['binance'] > 50:
            self.error_counts['binance'] = 25
        if self.error_counts['bybit'] > 50:
            self.error_counts['bybit'] = 25

class DexCexArbitrageEngine(ArbitrageEngine):
    """Enhanced ArbitrageEngine dengan DEX-CEX arbitrage support"""

    def __init__(self):
        super().__init__()
        self.dexscreener_client = DexScreenerClient()
        self.solana_validator = SolanaValidator()

        # DEX-specific stats
        self.dex_stats = {
            'total_dex_pairs_scanned': 0,
            'dex_opportunities_found': 0,
            'average_dex_liquidity': 0,
            'dex_api_success_rate': 0,
            'cex_vs_dex_ratio': 0,
            'solana_pairs_active': 0,
            'average_price_impact': 0,
            'dex_scan_duration': 0,
            'dexscreener_api_calls': 0
        }

        # DEX opportunity cache
        self.dex_opportunities = []
        self.combined_opportunities = []

        # Token matching cache untuk performance
        self.token_match_cache = {}
        self.cache_cleanup_interval = 300  # 5 minutes
        self.last_cache_cleanup = time.time()

    def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """Enhanced scan dengan DEX-CEX support"""
        start_time = time.time()

        try:
            # Scan CEX-CEX opportunities (existing functionality)
            cex_opportunities = super().scan_opportunities()

            # Scan DEX-CEX opportunities (new functionality)
            dex_opportunities = self._scan_dex_cex_opportunities()

            # Combine and sort all opportunities
            all_opportunities = cex_opportunities + dex_opportunities
            all_opportunities.sort(key=lambda x: (
                x.reliability_score,
                x.net_profit,
                -x.slippage_estimate
            ), reverse=True)

            # Update combined stats
            self._update_combined_stats(cex_opportunities, dex_opportunities, start_time)

            # Store for API access
            self.combined_opportunities = all_opportunities

            logger.info(f"[DEX-INFO] Combined scan completed: {len(cex_opportunities)} CEX-CEX + "
                       f"{len(dex_opportunities)} DEX-CEX = {len(all_opportunities)} total opportunities")

            return all_opportunities

        except Exception as e:
            logger.error(f"[DEX-ERROR] Enhanced scan failed: {e}")
            # Fallback to CEX-only scan
            return super().scan_opportunities()

    def _scan_dex_cex_opportunities(self) -> List[ArbitrageOpportunity]:
        """Scan DEX-CEX arbitrage opportunities"""
        dex_start_time = time.time()
        dex_opportunities = []

        try:
            # Get CEX symbols untuk matching
            cex_symbols = self.priority_symbols[:50]  # Limit untuk performance

            # Cleanup cache periodically
            if time.time() - self.last_cache_cleanup > self.cache_cleanup_interval:
                self._cleanup_token_cache()
                self.dexscreener_client.cleanup_cache()
                self.last_cache_cleanup = time.time()

            # Parallel processing untuk DEX data
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = []

                for symbol in cex_symbols:
                    future = executor.submit(self._process_dex_symbol, symbol)
                    futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    try:
                        opportunities = future.result()
                        if opportunities:
                            dex_opportunities.extend(opportunities)
                    except Exception as e:
                        logger.error(f"[DEX-ERROR] DEX symbol processing failed: {e}")

            # Update DEX-specific stats
            self.dex_stats['dex_scan_duration'] = time.time() - dex_start_time
            self.dex_stats['dex_opportunities_found'] = len(dex_opportunities)
            self.dex_stats['dex_api_success_rate'] = self.dexscreener_client.get_success_rate()

            if dex_opportunities:
                avg_liquidity = sum(op.dex_liquidity_usd or 0 for op in dex_opportunities) / len(dex_opportunities)
                self.dex_stats['average_dex_liquidity'] = avg_liquidity

                avg_impact = sum(op.price_impact_estimate or 0 for op in dex_opportunities) / len(dex_opportunities)
                self.dex_stats['average_price_impact'] = avg_impact

            logger.info(f"[DEX-INFO] DEX scan completed: {len(dex_opportunities)} opportunities found "
                       f"in {self.dex_stats['dex_scan_duration']:.2f}s")

            return dex_opportunities

        except Exception as e:
            logger.error(f"[DEX-ERROR] DEX scan failed: {e}")
            return []

    def _process_dex_symbol(self, symbol: str) -> List[ArbitrageOpportunity]:
        """Process single CEX symbol untuk DEX matching"""
        opportunities = []

        try:
            # Check cache first
            cache_key = f"dex_match_{symbol}"
            if cache_key in self.token_match_cache:
                cached_data = self.token_match_cache[cache_key]
                if time.time() - cached_data['timestamp'] < 300:  # 5 minute cache
                    return cached_data['opportunities']

            # Search token di DexScreener
            search_result = self.dexscreener_client.search_token(symbol.replace('USDT', ''))
            self.dex_stats['dexscreener_api_calls'] += 1

            if not search_result or 'pairs' not in search_result:
                return []

            # Filter untuk Solana pairs
            solana_pairs = [
                pair for pair in search_result['pairs']
                if self.solana_validator.is_solana_chain(pair.get('chainId', ''))
            ]

            self.dex_stats['total_dex_pairs_scanned'] += len(solana_pairs)
            self.dex_stats['solana_pairs_active'] += len(solana_pairs)

            # Get CEX prices untuk comparison
            cex_prices = self._get_cex_prices_for_symbol(symbol)
            if not cex_prices:
                return []

            # Process each Solana pair
            for pair in solana_pairs:
                try:
                    opportunity = self._create_dex_cex_opportunity(symbol, pair, cex_prices)
                    if opportunity and opportunity.execution_feasibility:
                        opportunities.append(opportunity)
                except Exception as e:
                    logger.error(f"[DEX-ERROR] Failed to process pair {pair.get('pairAddress', 'unknown')}: {e}")

            # Cache results
            self.token_match_cache[cache_key] = {
                'opportunities': opportunities,
                'timestamp': time.time()
            }

            return opportunities

        except Exception as e:
            logger.error(f"[DEX-ERROR] DEX symbol processing failed for {symbol}: {e}")
            return []

    def _get_cex_prices_for_symbol(self, symbol: str) -> Optional[Dict]:
        """Get current CEX prices untuk symbol"""
        try:
            # Get from existing opportunities or fetch fresh data
            for op in self.opportunities:
                if op.symbol == symbol:
                    return {
                        'binance_price': op.binance_price,
                        'bybit_price': op.bybit_price,
                        'binance_volume': op.binance_volume,
                        'bybit_volume': op.bybit_volume
                    }

            # If not found in current opportunities, return None
            # In production, could fetch fresh data here
            return None

        except Exception as e:
            logger.error(f"[DEX-ERROR] Failed to get CEX prices for {symbol}: {e}")
            return None

    def _create_dex_cex_opportunity(self, symbol: str, pair_data: Dict, cex_prices: Dict) -> Optional[ArbitrageOpportunity]:
        """Create DEX-CEX arbitrage opportunity"""
        try:
            # Validate pair data
            if not self.validation_engine.validate_dex_pair_status(pair_data):
                return None

            # Extract DEX data
            dex_price = float(pair_data.get('priceUsd', 0))
            if dex_price <= 0:
                return None

            # Get liquidity data
            liquidity_data = pair_data.get('liquidity', {})
            dex_liquidity_usd = float(liquidity_data.get('usd', 0))

            # Get volume data
            volume_data = pair_data.get('volume', {})
            dex_volume_24h = float(volume_data.get('h24', 0))

            # Calculate best CEX price untuk arbitrage
            binance_price = cex_prices['binance_price']
            bybit_price = cex_prices['bybit_price']

            # Determine arbitrage direction dan profit
            cex_to_dex_profit = ((dex_price - binance_price) / binance_price) * 100
            dex_to_cex_profit = ((bybit_price - dex_price) / dex_price) * 100

            if cex_to_dex_profit > dex_to_cex_profit and cex_to_dex_profit > 1.5:
                # CEX to DEX arbitrage
                profit_percentage = cex_to_dex_profit
                direction = "cex_to_dex"
                cex_price = binance_price
                cex_volume = cex_prices['binance_volume']
            elif dex_to_cex_profit > 1.5:
                # DEX to CEX arbitrage
                profit_percentage = dex_to_cex_profit
                direction = "dex_to_cex"
                cex_price = bybit_price
                cex_volume = cex_prices['bybit_volume']
            else:
                # Profit too low
                return None

            # Enhanced filtering untuk DEX-CEX
            if dex_liquidity_usd < 5000 or dex_volume_24h < 15000:
                return None

            # Calculate price impact
            trade_amount = 1000  # Assume $1000 trade
            price_impact = self.validation_engine.calculate_dex_price_impact(trade_amount, dex_liquidity_usd)

            # Get additional pair info
            base_token = pair_data.get('baseToken', {})
            dex_name = self.solana_validator.normalize_dex_name(pair_data.get('dexId', ''))

            # Create opportunity
            opportunity = ArbitrageOpportunity(
                symbol=symbol,
                binance_price=binance_price,
                bybit_price=bybit_price,
                profit_percentage=profit_percentage,
                volume_24h=min(cex_volume, dex_volume_24h),
                binance_volume=cex_prices['binance_volume'],
                bybit_volume=cex_prices['bybit_volume'],
                direction=direction,
                timestamp=datetime.now(),
                networks=['SOL'],  # Solana network
                arbitrage_type="DEX-CEX",

                # DEX-specific fields
                dex_name=dex_name,
                dex_pair_address=pair_data.get('pairAddress'),
                dex_liquidity_usd=dex_liquidity_usd,
                token_mint_address=base_token.get('address'),
                dex_volume_24h=dex_volume_24h,
                price_impact_estimate=price_impact,
                dex_link=f"https://dexscreener.com/solana/{pair_data.get('pairAddress')}",
                gas_fee_estimate=self.validation_engine.estimate_solana_gas_fees(),
                dex_trade_count_24h=pair_data.get('txns', {}).get('h24', {}).get('buys', 0) +
                                   pair_data.get('txns', {}).get('h24', {}).get('sells', 0)
            )

            # Apply enhanced validation
            opportunity = self.validation_engine.validate_opportunity(opportunity)
            opportunity = self.validation_engine.validate_dex_opportunity(opportunity)

            return opportunity

        except Exception as e:
            logger.error(f"[DEX-ERROR] Failed to create DEX-CEX opportunity: {e}")
            return None

    def _update_combined_stats(self, cex_opportunities: List, dex_opportunities: List, start_time: float):
        """Update combined statistics"""
        # Update existing CEX stats
        self._update_enhanced_stats(cex_opportunities + dex_opportunities, start_time)

        # Calculate DEX vs CEX ratio
        total_ops = len(cex_opportunities) + len(dex_opportunities)
        if total_ops > 0:
            self.dex_stats['cex_vs_dex_ratio'] = len(dex_opportunities) / total_ops

        # Merge DEX stats into main stats
        self.stats.update(self.dex_stats)

    def _cleanup_token_cache(self):
        """Cleanup expired token match cache"""
        current_time = time.time()
        expired_keys = []

        for key, data in self.token_match_cache.items():
            if current_time - data['timestamp'] > 300:  # 5 minutes
                expired_keys.append(key)

        for key in expired_keys:
            del self.token_match_cache[key]

        if expired_keys:
            logger.info(f"[DEX-INFO] Cleaned up {len(expired_keys)} expired token cache entries")

    def get_dex_status(self) -> Dict:
        """Get DEX-specific status information"""
        return {
            'dexscreener_healthy': self.dexscreener_client.health_check(),
            'dex_api_success_rate': self.dexscreener_client.get_success_rate(),
            'dex_cache_size': len(self.dexscreener_client.cache),
            'token_match_cache_size': len(self.token_match_cache),
            'dex_stats': self.dex_stats
        }

    def get_opportunities_by_type(self, arbitrage_type: str = "ALL") -> List[ArbitrageOpportunity]:
        """Get opportunities filtered by arbitrage type"""
        if arbitrage_type == "CEX-CEX":
            return [op for op in self.combined_opportunities if op.arbitrage_type == "CEX-CEX"]
        elif arbitrage_type == "DEX-CEX":
            return [op for op in self.combined_opportunities if op.arbitrage_type == "DEX-CEX"]
        else:
            return self.combined_opportunities

    def _process_symbol(self, symbol: str, binance_item: Dict, bybit_item: Dict) -> Optional[ArbitrageOpportunity]:
        """Process single symbol untuk arbitrase"""
        try:
            # Parse Binance data
            binance_price = float(binance_item.get('lastPrice', 0))
            binance_volume = float(binance_item.get('quoteVolume', 0))

            # Parse Bybit data
            bybit_price = float(bybit_item.get('lastPrice', 0))
            bybit_volume = float(bybit_item.get('turnover24h', 0))

            return self.calculate_arbitrage(symbol, binance_price, bybit_price, binance_volume, bybit_volume)

        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            return None

    def get_status(self) -> Dict:
        """Status sistem"""
        return {
            'binance_healthy': self.binance_client.health_check(),
            'bybit_healthy': self.bybit_client.health_check(),
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'opportunities_count': len(self.opportunities),
            'stats': self.stats
        }

# Flask Application
app = Flask(__name__)
app.config['SECRET_KEY'] = 'cryptoarb-pro-2024'

# Global arbitrage engine dengan DEX-CEX support
arbitrage_engine = DexCexArbitrageEngine()

@app.route('/')
def index():
    """Halaman utama"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/opportunities')
def get_opportunities():
    """Enhanced API endpoint untuk peluang arbitrase dengan DEX-CEX support"""
    try:
        # Enhanced filtering parameters
        min_profit = float(request.args.get('min_profit', 0.5))
        max_profit = float(request.args.get('max_profit', 200))
        min_volume = float(request.args.get('min_volume', 10000))
        min_reliability = float(request.args.get('min_reliability', 0))
        only_executable = request.args.get('only_executable', 'false').lower() == 'true'
        arbitrage_type = request.args.get('arbitrage_type', 'ALL')  # ALL, CEX-CEX, DEX-CEX
        dex_name = request.args.get('dex_name', '')  # Filter by specific DEX

        # Get opportunities by type
        if hasattr(arbitrage_engine, 'get_opportunities_by_type'):
            opportunities = arbitrage_engine.get_opportunities_by_type(arbitrage_type)
        else:
            opportunities = arbitrage_engine.opportunities

        filtered_opportunities = []
        for op in opportunities:
            # Enhanced filtering logic
            if (min_profit <= op.profit_percentage <= max_profit and
                op.volume_24h >= min_volume and
                getattr(op, 'reliability_score', 0) >= min_reliability and
                (not only_executable or getattr(op, 'execution_feasibility', True)) and
                (not dex_name or getattr(op, 'dex_name', '').lower() == dex_name.lower())):

                # Base opportunity data
                opportunity_data = {
                    'symbol': op.symbol,
                    'binance_price': op.binance_price,
                    'bybit_price': op.bybit_price,
                    'profit_percentage': round(op.profit_percentage, 3),
                    'net_profit': round(op.net_profit, 3),
                    'volume_24h': op.volume_24h,
                    'binance_volume': getattr(op, 'binance_volume', 0),
                    'bybit_volume': getattr(op, 'bybit_volume', 0),
                    'direction': op.direction,
                    'timestamp': op.timestamp.isoformat(),
                    'networks': op.networks,
                    'binance_link': f"https://www.binance.com/en/trade/{op.symbol}",
                    'bybit_link': f"https://www.bybit.com/en/trade/spot/{op.symbol}",
                    # Enhanced data
                    'reliability_score': round(getattr(op, 'reliability_score', 0), 1),
                    'quality_grade': getattr(op, 'quality_grade', 'C'),
                    'risk_level': getattr(op, 'risk_level', 'Sedang'),
                    'slippage_estimate': round(getattr(op, 'slippage_estimate', 0), 3),
                    'market_volatility': round(getattr(op, 'market_volatility', 0), 2),
                    'execution_feasibility': getattr(op, 'execution_feasibility', True),
                    'price_trend': getattr(op, 'price_trend', 'stable'),
                    'network_congestion': getattr(op, 'network_congestion', {}),
                    'arbitrage_type': getattr(op, 'arbitrage_type', 'CEX-CEX')
                }

                # Add DEX-specific data if available
                if getattr(op, 'arbitrage_type', 'CEX-CEX') == 'DEX-CEX':
                    opportunity_data.update({
                        'dex_name': getattr(op, 'dex_name', ''),
                        'dex_pair_address': getattr(op, 'dex_pair_address', ''),
                        'dex_liquidity_usd': getattr(op, 'dex_liquidity_usd', 0),
                        'token_mint_address': getattr(op, 'token_mint_address', ''),
                        'dex_volume_24h': getattr(op, 'dex_volume_24h', 0),
                        'price_impact_estimate': round(getattr(op, 'price_impact_estimate', 0), 3),
                        'dex_link': getattr(op, 'dex_link', ''),
                        'gas_fee_estimate': round(getattr(op, 'gas_fee_estimate', 0), 4),
                        'dex_trade_count_24h': getattr(op, 'dex_trade_count_24h', 0)
                    })

                filtered_opportunities.append(opportunity_data)

        # Sort by reliability score and profit
        filtered_opportunities.sort(key=lambda x: (x['reliability_score'], x['profit_percentage']), reverse=True)

        return jsonify({
            'success': True,
            'data': filtered_opportunities,
            'count': len(filtered_opportunities),
            'metadata': {
                'scan_time': arbitrage_engine.stats.get('scan_duration', 0),
                'total_scanned': arbitrage_engine.stats.get('total_scanned', 0),
                'high_quality_count': arbitrage_engine.stats.get('high_quality_count', 0),
                'executable_count': arbitrage_engine.stats.get('executable_count', 0),
                'average_reliability': round(arbitrage_engine.stats.get('average_reliability', 0), 1)
            }
        })

    except Exception as e:
        logger.error(f"Enhanced API error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """Enhanced API endpoint untuk status sistem dengan DEX support"""
    status = arbitrage_engine.get_status()

    # Add DEX status if available
    if hasattr(arbitrage_engine, 'get_dex_status'):
        dex_status = arbitrage_engine.get_dex_status()
        status.update(dex_status)

    return jsonify(status)

@app.route('/api/dex/status')
def get_dex_status():
    """API endpoint khusus untuk DEX status"""
    if hasattr(arbitrage_engine, 'get_dex_status'):
        return jsonify({
            'success': True,
            'data': arbitrage_engine.get_dex_status()
        })
    else:
        return jsonify({
            'success': False,
            'error': 'DEX functionality not available'
        }), 404

@app.route('/api/scan')
def trigger_scan():
    """Trigger manual scan"""
    try:
        opportunities = arbitrage_engine.scan_opportunities()
        return jsonify({
            'success': True,
            'opportunities_found': len(opportunities),
            'scan_time': arbitrage_engine.stats['scan_duration']
        })
    except Exception as e:
        logger.error(f"Manual scan error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# HTML Template dengan Dark Futuristic Theme
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoArb Pro - Deteksi Arbitrase Cryptocurrency</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
            --card-bg: rgba(255, 255, 255, 0.1);
            --accent-green: #00ff88;
            --accent-red: #ff4757;
            --accent-blue: #3742fa;
            --text-primary: #ffffff;
            --text-secondary: #a4a4a4;
            --border-color: rgba(255, 255, 255, 0.2);
            --shadow-glow: 0 8px 32px rgba(0, 255, 136, 0.1);
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --border-radius: 15px;
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 20px;
            --spacing-lg: 30px;
            --spacing-xl: 40px;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
            font-size: 14px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: var(--spacing-md);
            width: 100%;
        }

        /* Improved Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: var(--spacing-sm);
        }

        h1 { font-size: clamp(1.8rem, 4vw, 2.5rem); }
        h2 { font-size: clamp(1.5rem, 3vw, 2rem); }
        h3 { font-size: clamp(1.2rem, 2.5vw, 1.5rem); }

        p, span, div {
            font-size: clamp(0.875rem, 1.5vw, 1rem);
        }

        /* Enhanced Header */
        .header {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: var(--spacing-md);
            align-items: center;
            box-shadow: var(--shadow-glow);
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .logo {
            font-size: clamp(1.5rem, 4vw, 2rem);
            font-weight: 700;
            background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
            position: relative;
            z-index: 1;
            white-space: nowrap;
        }

        .status-indicators {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--card-bg);
            border-radius: 25px;
            border: 1px solid var(--border-color);
            transition: var(--transition-smooth);
            font-size: 0.875rem;
            white-space: nowrap;
        }

        .status-indicator:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            animation: pulse 2s infinite;
            flex-shrink: 0;
        }

        .status-dot.healthy {
            background: var(--accent-green);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        .status-dot.unhealthy {
            background: var(--accent-red);
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }
        .status-dot.loading {
            background: #ffa502;
            box-shadow: 0 0 10px rgba(255, 165, 2, 0.5);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .current-time {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-align: right;
            font-family: 'Courier New', monospace;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Dashboard Layout */
        .dashboard {
            display: grid;
            grid-template-columns: minmax(280px, 320px) 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            align-items: start;
        }

        /* Enhanced Sidebar */
        .sidebar {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            height: fit-content;
            position: sticky;
            top: var(--spacing-md);
            transition: var(--transition-smooth);
        }

        .sidebar:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        .sidebar h3 {
            color: var(--accent-green);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .filter-group {
            margin-bottom: var(--spacing-md);
            position: relative;
        }

        .filter-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            transition: var(--transition-smooth);
        }

        .filter-input, .filter-select {
            width: 100%;
            padding: var(--spacing-sm);
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: var(--accent-green);
            box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
            transform: translateY(-1px);
        }

        .filter-input:hover, .filter-select:hover {
            border-color: rgba(255, 255, 255, 0.4);
        }

        /* Enhanced Range Slider */
        .range-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, var(--border-color), var(--accent-green));
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
            transition: var(--transition-smooth);
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-green);
            cursor: pointer;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
            transition: var(--transition-smooth);
        }

        .range-slider::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
        }

        .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-green);
            cursor: pointer;
            border: none;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
        }

        /* Range Value Display */
        .range-value {
            display: inline-block;
            margin-top: var(--spacing-xs);
            padding: 2px 8px;
            background: var(--accent-green);
            color: #000;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Enhanced Buttons with Animations */
        .btn {
            background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 25px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-smooth);
            width: 100%;
            margin-top: var(--spacing-sm);
            position: relative;
            overflow: hidden;
            font-size: 0.875rem;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 255, 136, 0.4);
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        /* Ripple Effect */
        .btn-ripple {
            position: relative;
            overflow: hidden;
        }

        .btn-ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn-ripple:active::after {
            width: 300px;
            height: 300px;
        }

        /* Main Content */
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .content-title {
            font-size: 24px;
            color: #00ff88;
        }

        .scan-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .auto-scan-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #00ff88;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(25px);
        }

        /* Enhanced Opportunities Table with Animations */
        .table-container {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-top: var(--spacing-md);
            box-shadow: var(--shadow-glow);
            animation: slideInUp 0.6s ease-out;
        }

        .opportunities-table {
            width: 100%;
            border-collapse: collapse;
        }

        .opportunities-table th,
        .opportunities-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition-smooth);
        }

        .opportunities-table th {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(55, 66, 250, 0.1));
            color: var(--accent-green);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .opportunities-table tbody tr {
            transition: var(--transition-smooth);
            animation: fadeInRow 0.5s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .opportunities-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
        .opportunities-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
        .opportunities-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
        .opportunities-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
        .opportunities-table tbody tr:nth-child(5) { animation-delay: 0.5s; }

        @keyframes fadeInRow {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .opportunities-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.05), rgba(55, 66, 250, 0.05));
            transform: translateY(-2px) scale(1.01);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.1);
        }

        .opportunities-table tbody tr:hover td {
            border-color: var(--accent-green);
        }

        /* Floating Card Effect */
        .card-float {
            animation: float-gentle 6s ease-in-out infinite;
        }

        @keyframes float-gentle {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        /* Enhanced Profit Indicators with Animations */
        .profit-positive {
            color: var(--accent-green);
            font-weight: 600;
            transition: var(--transition-smooth);
        }

        .profit-high {
            color: var(--accent-green);
            font-weight: 700;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
            animation: profit-glow 2s ease-in-out infinite alternate;
            position: relative;
        }

        .profit-high::before {
            content: '🔥';
            position: absolute;
            left: -20px;
            animation: bounce 1s ease-in-out infinite;
        }

        @keyframes profit-glow {
            from {
                text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
                transform: scale(1);
            }
            to {
                text-shadow: 0 0 25px rgba(0, 255, 136, 1);
                transform: scale(1.05);
            }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        /* Animated Counters */
        .animated-counter {
            display: inline-block;
            transition: var(--transition-smooth);
        }

        .counter-up {
            animation: countUp 0.8s ease-out;
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Breathing Animation for High Value Items */
        .breathing {
            animation: breathe 3s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.02);
                opacity: 0.9;
            }
        }

        .direction-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .direction-binance {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .direction-bybit {
            background: rgba(255, 87, 87, 0.2);
            color: #ff5757;
            border: 1px solid rgba(255, 87, 87, 0.3);
        }

        /* Grade Badges */
        .grade-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 700;
            text-align: center;
            min-width: 30px;
            display: inline-block;
        }

        .grade-excellent {
            background: linear-gradient(45deg, var(--accent-green), #00d4aa);
            color: #000;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .grade-good {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: #fff;
        }

        .grade-average {
            background: linear-gradient(45deg, #ffa726, #ff9800);
            color: #fff;
        }

        .grade-poor {
            background: linear-gradient(45deg, #ff7043, #ff5722);
            color: #fff;
        }

        .grade-fail {
            background: linear-gradient(45deg, var(--accent-red), #d32f2f);
            color: #fff;
        }

        /* Tab Navigation System */
        .tab-navigation {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: var(--spacing-sm);
        }

        .tab-button {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px 8px 0 0;
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
            transition: width 0.3s ease;
        }

        .tab-button:hover {
            background: rgba(255, 255, 255, 0.15);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .tab-button.active {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(55, 66, 250, 0.1));
            color: var(--accent-green);
            border-color: var(--accent-green);
        }

        .tab-button.active::before {
            width: 100%;
        }

        /* Tab Content */
        .tab-content {
            position: relative;
        }

        .tab-pane {
            display: none;
            animation: fadeInTab 0.4s ease-out;
        }

        .tab-pane.active {
            display: block;
        }

        @keyframes fadeInTab {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* DEX-specific Filters */
        .dex-filters {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            backdrop-filter: blur(10px);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            align-items: end;
        }

        /* DEX-specific Badges */
        .arbitrage-type-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .arbitrage-cex-cex {
            background: linear-gradient(45deg, var(--accent-blue), #4c6ef5);
            color: #fff;
        }

        .arbitrage-dex-cex {
            background: linear-gradient(45deg, #9c27b0, #e91e63);
            color: #fff;
            animation: pulse-dex 2s ease-in-out infinite;
        }

        @keyframes pulse-dex {
            0%, 100% {
                box-shadow: 0 0 5px rgba(156, 39, 176, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(156, 39, 176, 0.8);
            }
        }

        /* DEX Platform Icons */
        .dex-platform-icon {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.875rem;
        }

        .dex-platform-icon::before {
            content: '🔄';
            font-size: 1rem;
        }

        .dex-raydium::before { content: '⚡'; }
        .dex-orca::before { content: '🐋'; }
        .dex-jupiter::before { content: '🪐'; }
        .dex-serum::before { content: '🧬'; }

        .exchange-links {
            display: flex;
            gap: 10px;
        }

        .exchange-link {
            padding: 6px 12px;
            background: rgba(55, 66, 250, 0.2);
            color: #3742fa;
            text-decoration: none;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(55, 66, 250, 0.3);
            transition: all 0.3s ease;
        }

        .exchange-link:hover {
            background: rgba(55, 66, 250, 0.3);
            transform: translateY(-2px);
        }

        /* Enhanced Loading States with Particle Effects */
        .loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
            position: relative;
        }

        .loading-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        /* Geometric Loading Animation */
        .spinner {
            width: 50px;
            height: 50px;
            position: relative;
        }

        .spinner::before,
        .spinner::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            animation: pulse-ring 2s ease-out infinite;
        }

        .spinner::before {
            width: 100%;
            height: 100%;
            border: 3px solid var(--accent-green);
            animation: spin 1.5s linear infinite;
        }

        .spinner::after {
            width: 80%;
            height: 80%;
            top: 10%;
            left: 10%;
            border: 2px solid var(--accent-blue);
            animation: spin 1s linear infinite reverse;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* Particle Effects */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent-green);
            border-radius: 50%;
            animation: float 3s ease-in-out infinite;
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 0.5s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 1s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 1.5s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 2s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 2.5s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 3s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 3.5s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% {
                transform: translateY(0) scale(1);
                opacity: 0;
            }
            50% {
                transform: translateY(-30px) scale(1.2);
                opacity: 1;
            }
        }

        /* Loading Text Animation */
        .loading-text {
            margin-top: var(--spacing-md);
            font-size: 1rem;
            color: var(--text-secondary);
            animation: pulse-text 2s ease-in-out infinite;
        }

        @keyframes pulse-text {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        /* Footer Stats */
        .footer-stats {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #a4a4a4;
            font-size: 14px;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            margin: 5% auto;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .close {
            color: #a4a4a4;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ff4757;
        }

        /* Gradient Background Animations */
        .gradient-bg {
            background: linear-gradient(-45deg, #0a0a0a, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradient-shift 15s ease infinite;
        }

        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Slide-in Animations for New Opportunities */
        .opportunity-new {
            animation: slideInFromRight 0.6s ease-out;
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 280px 1fr;
                gap: var(--spacing-md);
            }

            .container {
                padding: var(--spacing-sm);
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .sidebar {
                position: static;
                order: 2;
            }

            .main-content {
                order: 1;
            }

            .header {
                grid-template-columns: 1fr;
                text-align: center;
                gap: var(--spacing-sm);
            }

            .status-indicators {
                justify-content: center;
                gap: var(--spacing-xs);
            }

            .status-indicator {
                font-size: 0.75rem;
                padding: 6px 10px;
            }

            .opportunities-table {
                font-size: 0.75rem;
            }

            .opportunities-table th,
            .opportunities-table td {
                padding: var(--spacing-xs);
            }

            .logo {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: var(--spacing-xs);
            }

            .header {
                padding: var(--spacing-sm);
            }

            .sidebar {
                padding: var(--spacing-sm);
            }

            .opportunities-table {
                font-size: 0.7rem;
            }

            .opportunities-table th,
            .opportunities-table td {
                padding: 6px;
            }

            .filter-group {
                margin-bottom: var(--spacing-sm);
            }

            .btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: 0.75rem;
            }
        }

        /* High DPI Display Optimizations */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .status-dot {
                width: 12px;
                height: 12px;
            }

            .spinner::before,
            .spinner::after {
                border-width: 2px;
            }
        }

        /* Dark Mode Enhancements */
        @media (prefers-color-scheme: dark) {
            :root {
                --shadow-glow: 0 8px 32px rgba(0, 255, 136, 0.15);
            }
        }

        /* Reduced Motion for Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 136, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1001;
        }

        .toast.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚀 CryptoArb Pro</div>
            <div class="status-indicators">
                <div class="status-indicator">
                    <div class="status-dot loading" id="binance-status"></div>
                    <span>Binance</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot loading" id="bybit-status"></div>
                    <span>Bybit</span>
                </div>
                <div class="current-time" id="current-time"></div>
            </div>
        </div>

        <!-- Dashboard -->
        <div class="dashboard">
            <!-- Sidebar Filters -->
            <div class="sidebar">
                <h3>🎛️ Filter Peluang</h3>

                <div class="filter-group">
                    <label for="min-profit">Profit Minimum (%)</label>
                    <input type="range" id="min-profit" class="range-slider" min="0.5" max="50" value="0.5" step="0.1">
                    <span id="min-profit-value" class="range-value">0.5%</span>
                </div>

                <div class="filter-group">
                    <label for="max-profit">Profit Maksimum (%)</label>
                    <input type="range" id="max-profit" class="range-slider" min="1" max="200" value="200" step="1">
                    <span id="max-profit-value" class="range-value">200%</span>
                </div>

                <div class="filter-group">
                    <label for="min-volume">Volume Minimum ($)</label>
                    <input type="number" id="min-volume" class="filter-input" value="10000" min="1000" step="1000">
                </div>

                <div class="filter-group">
                    <label for="network-filter">Jaringan</label>
                    <select id="network-filter" class="filter-input">
                        <option value="">Semua Jaringan</option>
                        <option value="ETH">Ethereum</option>
                        <option value="BSC">Binance Smart Chain</option>
                        <option value="TRC20">Tron</option>
                        <option value="POLYGON">Polygon</option>
                        <option value="ARBITRUM">Arbitrum</option>
                    </select>
                </div>

                <button class="btn btn-ripple" onclick="applyFilters()">🔍 Terapkan Filter</button>
                <button class="btn btn-ripple" onclick="triggerManualScan()">🔄 Scan Manual</button>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-header">
                    <h2 class="content-title">📊 Peluang Arbitrase Real-time</h2>
                    <div class="scan-controls">
                        <div class="auto-scan-toggle">
                            <span>Auto Scan</span>
                            <div class="toggle-switch active" id="auto-scan-toggle" onclick="toggleAutoScan()"></div>
                        </div>
                        <span id="last-update">Belum ada data</span>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-button active" onclick="switchTab('cex-cex')" id="tab-cex-cex">
                        🏦 CEX-CEX Arbitrase
                    </button>
                    <button class="tab-button" onclick="switchTab('dex-cex')" id="tab-dex-cex">
                        🔄 DEX-CEX Arbitrase
                    </button>
                    <button class="tab-button" onclick="switchTab('all')" id="tab-all">
                        📊 Semua Peluang
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <div id="tab-content-cex-cex" class="tab-pane active">
                        <div id="opportunities-container-cex"></div>
                    </div>
                    <div id="tab-content-dex-cex" class="tab-pane">
                        <div class="dex-filters">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label for="dex-selection">DEX Platform</label>
                                    <select id="dex-selection" class="filter-input">
                                        <option value="">Semua DEX</option>
                                        <option value="raydium">Raydium</option>
                                        <option value="orca">Orca</option>
                                        <option value="jupiter">Jupiter</option>
                                        <option value="serum">Serum</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="min-dex-liquidity">Likuiditas DEX Minimum ($)</label>
                                    <input type="range" id="min-dex-liquidity" class="range-slider" min="1000" max="100000" value="5000" step="1000">
                                    <span id="min-dex-liquidity-value" class="range-value">$5,000</span>
                                </div>
                                <div class="filter-group">
                                    <label for="max-price-impact">Price Impact Maksimum (%)</label>
                                    <input type="range" id="max-price-impact" class="range-slider" min="0.1" max="5" value="2" step="0.1">
                                    <span id="max-price-impact-value" class="range-value">2.0%</span>
                                </div>
                            </div>
                        </div>
                        <div id="opportunities-container-dex"></div>
                    </div>
                    <div id="tab-content-all" class="tab-pane">
                        <div id="opportunities-container-all"></div>
                    </div>
                </div>
                    <div class="loading">
                        <div class="loading-container">
                            <div class="spinner"></div>
                            <div class="particles">
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                            </div>
                        </div>
                        <div class="loading-text">Memuat peluang arbitrase...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Stats -->
        <div class="footer-stats">
            <div class="stat-item">
                <div class="stat-value" id="total-opportunities">0</div>
                <div class="stat-label">Total Peluang</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="average-profit">0%</div>
                <div class="stat-label">Rata-rata Profit</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="scan-duration">0s</div>
                <div class="stat-label">Waktu Scan</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="tokens-scanned">0</div>
                <div class="stat-label">Token Dipindai</div>
            </div>
        </div>
    </div>

    <!-- Modal Detail -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modal-content"></div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>

    <script>
        let autoScanEnabled = true;
        let scanInterval;
        let currentFilters = {
            min_profit: 0.5,
            max_profit: 200,
            min_volume: 10000,
            network: ''
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            checkSystemStatus();
            setInterval(checkSystemStatus, 10000);

            // Load initial tab
            loadOpportunitiesForTab(currentTab);
            startAutoScan();

            // Setup filter event listeners
            setupFilterListeners();
            setupDexFilterListeners();
        });

        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent =
                now.toLocaleString('id-ID', {
                    timeZone: 'Asia/Jakarta',
                    hour12: false
                });
        }

        function setupFilterListeners() {
            const minProfitSlider = document.getElementById('min-profit');
            const maxProfitSlider = document.getElementById('max-profit');

            minProfitSlider.addEventListener('input', function() {
                document.getElementById('min-profit-value').textContent = this.value + '%';
                currentFilters.min_profit = parseFloat(this.value);
            });

            maxProfitSlider.addEventListener('input', function() {
                document.getElementById('max-profit-value').textContent = this.value + '%';
                currentFilters.max_profit = parseFloat(this.value);
            });
        }

        function setupDexFilterListeners() {
            // DEX-specific filter listeners
            const dexLiquiditySlider = document.getElementById('min-dex-liquidity');
            const priceImpactSlider = document.getElementById('max-price-impact');
            const dexSelection = document.getElementById('dex-selection');

            if (dexLiquiditySlider) {
                dexLiquiditySlider.addEventListener('input', function() {
                    const value = parseInt(this.value);
                    document.getElementById('min-dex-liquidity-value').textContent = '$' + formatNumber(value);
                    if (currentTab === 'dex-cex') {
                        loadOpportunitiesForTab(currentTab);
                    }
                });
            }

            if (priceImpactSlider) {
                priceImpactSlider.addEventListener('input', function() {
                    document.getElementById('max-price-impact-value').textContent = this.value + '%';
                    if (currentTab === 'dex-cex') {
                        loadOpportunitiesForTab(currentTab);
                    }
                });
            }

            if (dexSelection) {
                dexSelection.addEventListener('change', function() {
                    if (currentTab === 'dex-cex') {
                        loadOpportunitiesForTab(currentTab);
                    }
                });
            }
        }

        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                // Update status indicators
                const binanceStatus = document.getElementById('binance-status');
                const bybitStatus = document.getElementById('bybit-status');

                binanceStatus.className = 'status-dot ' + (data.binance_healthy ? 'healthy' : 'unhealthy');
                bybitStatus.className = 'status-dot ' + (data.bybit_healthy ? 'healthy' : 'unhealthy');

                // Update stats
                if (data.stats) {
                    document.getElementById('tokens-scanned').textContent = data.stats.total_scanned || 0;
                    document.getElementById('scan-duration').textContent = (data.stats.scan_duration || 0).toFixed(2) + 's';
                }

            } catch (error) {
                console.error('Status check failed:', error);
                document.getElementById('binance-status').className = 'status-dot unhealthy';
                document.getElementById('bybit-status').className = 'status-dot unhealthy';
            }
        }

        async function loadOpportunities() {
            // Use tab-based loading
            await loadOpportunitiesForTab(currentTab);
        }

        // Tab Management Functions
        let currentTab = 'cex-cex';
        let allOpportunities = [];

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`tab-${tabName}`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            document.getElementById(`tab-content-${tabName}`).classList.add('active');

            currentTab = tabName;

            // Load appropriate data
            loadOpportunitiesForTab(tabName);
        }

        async function loadOpportunitiesForTab(tabName) {
            try {
                let params = new URLSearchParams(currentFilters);

                // Add tab-specific filters
                if (tabName === 'cex-cex') {
                    params.set('arbitrage_type', 'CEX-CEX');
                } else if (tabName === 'dex-cex') {
                    params.set('arbitrage_type', 'DEX-CEX');

                    // Add DEX-specific filters
                    const dexSelection = document.getElementById('dex-selection')?.value;
                    if (dexSelection) {
                        params.set('dex_name', dexSelection);
                    }
                } else {
                    params.set('arbitrage_type', 'ALL');
                }

                const response = await fetch(`/api/opportunities?${params}`);
                const data = await response.json();

                if (data.success) {
                    allOpportunities = data.data;
                    displayOpportunitiesInTab(data.data, tabName);
                    updateStats(data.data);

                    // Update metadata if available
                    if (data.metadata) {
                        updateMetadataDisplay(data.metadata);
                    }
                } else {
                    showToast('❌ Gagal memuat peluang: ' + data.error, 'error');
                }

            } catch (error) {
                console.error('Load opportunities failed:', error);
                showToast('❌ Koneksi terputus', 'error');
            }
        }

        function displayOpportunitiesInTab(opportunities, tabName) {
            const containerId = tabName === 'cex-cex' ? 'opportunities-container-cex' :
                              tabName === 'dex-cex' ? 'opportunities-container-dex' :
                              'opportunities-container-all';

            const container = document.getElementById(containerId);

            if (!container) {
                console.error('Container not found:', containerId);
                return;
            }

            displayOpportunities(opportunities, container);
        }

        function displayOpportunities(opportunities, container = null) {
            if (!container) {
                container = document.getElementById('opportunities-container');
            }

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="card-float" style="text-align: center; padding: 50px; color: var(--text-secondary);">
                        <h3>📭 Tidak ada peluang arbitrase ditemukan</h3>
                        <p>Coba sesuaikan filter atau tunggu scan berikutnya</p>
                        <div class="particles">
                            <div class="particle"></div>
                            <div class="particle"></div>
                            <div class="particle"></div>
                        </div>
                    </div>
                `;
                return;
            }

            // Dynamic table headers berdasarkan tab
            let tableHeaders = `
                <th>💰 Symbol</th>
                <th>📈 Profit</th>
                <th>⭐ Grade</th>
                <th>💵 Harga Binance</th>
                <th>💵 Harga Bybit</th>
                <th>📊 Volume 24h</th>
                <th>🎯 Reliability</th>
            `;

            // Add DEX-specific headers
            if (currentTab === 'dex-cex' || currentTab === 'all') {
                tableHeaders += `
                    <th>🏷️ Type</th>
                    <th>🔄 DEX Platform</th>
                    <th>💧 DEX Liquidity</th>
                    <th>📉 Price Impact</th>
                `;
            }

            tableHeaders += `
                <th>🔄 Arah</th>
                <th>🔗 Trading</th>
                <th>ℹ️ Detail</th>
            `;

            let tableHTML = `
                <div class="table-container">
                    <table class="opportunities-table">
                        <thead>
                            <tr>${tableHeaders}</tr>
                        </thead>
                        <tbody>
            `;

            opportunities.forEach((op, index) => {
                const profitClass = op.profit_percentage > 5 ? 'profit-high breathing' : 'profit-positive';
                const gradeClass = getGradeClass(op.quality_grade || 'C');
                const reliabilityClass = op.reliability_score >= 80 ? 'profit-high' : op.reliability_score >= 60 ? 'profit-positive' : '';

                // Enhanced direction handling untuk DEX-CEX
                let directionClass, directionText, exchangeLinks;

                if (op.arbitrage_type === 'DEX-CEX') {
                    if (op.direction === 'cex_to_dex') {
                        directionClass = 'direction-binance';
                        directionText = `CEX → ${op.dex_name || 'DEX'}`;
                    } else {
                        directionClass = 'direction-bybit';
                        directionText = `${op.dex_name || 'DEX'} → CEX`;
                    }

                    exchangeLinks = `
                        <a href="${op.binance_link}" target="_blank" class="exchange-link">Binance</a>
                        <a href="${op.bybit_link}" target="_blank" class="exchange-link">Bybit</a>
                        ${op.dex_link ? `<a href="${op.dex_link}" target="_blank" class="exchange-link">DexScreener</a>` : ''}
                    `;
                } else {
                    directionClass = op.direction === 'binance_to_bybit' ? 'direction-binance' : 'direction-bybit';
                    directionText = op.direction === 'binance_to_bybit' ? 'Binance → Bybit' : 'Bybit → Binance';
                    exchangeLinks = `
                        <a href="${op.binance_link}" target="_blank" class="exchange-link">Binance</a>
                        <a href="${op.bybit_link}" target="_blank" class="exchange-link">Bybit</a>
                    `;
                }

                // Additional columns untuk DEX data
                let additionalColumns = '';
                if (currentTab === 'dex-cex' || currentTab === 'all') {
                    additionalColumns = `
                        <td>
                            <span class="arbitrage-type-badge arbitrage-${op.arbitrage_type.toLowerCase().replace('-', '-')}">${op.arbitrage_type}</span>
                        </td>
                        <td>
                            ${op.dex_name ? `<span class="dex-platform-icon dex-${op.dex_name.toLowerCase()}">${op.dex_name}</span>` : '-'}
                        </td>
                        <td>
                            ${op.dex_liquidity_usd ? '$' + formatNumber(op.dex_liquidity_usd) : '-'}
                        </td>
                        <td>
                            ${op.price_impact_estimate ? op.price_impact_estimate.toFixed(2) + '%' : '-'}
                        </td>
                    `;
                }

                tableHTML += `
                    <tr class="opportunity-row" onclick="showDetail('${op.symbol}', ${JSON.stringify(op).replace(/"/g, '&quot;')})" style="animation-delay: ${index * 0.1}s">
                        <td><strong>${op.symbol}</strong></td>
                        <td class="${profitClass}">
                            <span class="animated-counter">${op.profit_percentage.toFixed(3)}%</span>
                            ${op.net_profit ? `<br><small style="color: var(--text-secondary);">Net: ${op.net_profit.toFixed(2)}%</small>` : ''}
                        </td>
                        <td><span class="grade-badge ${gradeClass}">${op.quality_grade || 'C'}</span></td>
                        <td>$${op.binance_price.toFixed(6)}</td>
                        <td>$${op.bybit_price.toFixed(6)}</td>
                        <td>$${formatNumber(op.volume_24h)}</td>
                        <td class="${reliabilityClass}">${(op.reliability_score || 0).toFixed(1)}/100</td>
                        ${additionalColumns}
                        <td><span class="direction-badge ${directionClass}">${directionText}</span></td>
                        <td class="exchange-links">${exchangeLinks}</td>
                        <td><button onclick="event.stopPropagation(); showDetail('${op.symbol}', ${JSON.stringify(op).replace(/"/g, '&quot;')})" class="btn btn-ripple" style="padding: 5px 10px; margin: 0; font-size: 0.75rem;">Detail</button></td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table></div>';
            container.innerHTML = tableHTML;

            // Trigger counter animations
            animateCounters();

            // Update last update time with animation
            const lastUpdateEl = document.getElementById('last-update');
            lastUpdateEl.style.opacity = '0';
            setTimeout(() => {
                lastUpdateEl.textContent = 'Terakhir: ' + new Date().toLocaleTimeString('id-ID');
                lastUpdateEl.style.opacity = '1';
            }, 200);
        }

        function getGradeClass(grade) {
            switch(grade) {
                case 'A+': case 'A': return 'grade-excellent';
                case 'B': return 'grade-good';
                case 'C': return 'grade-average';
                case 'D': return 'grade-poor';
                case 'F': return 'grade-fail';
                default: return 'grade-average';
            }
        }

        function updateMetadataDisplay(metadata) {
            // Update enhanced statistics dari API metadata
            if (metadata.high_quality_count !== undefined) {
                // Could add display for high quality count
            }
            if (metadata.executable_count !== undefined) {
                // Could add display for executable count
            }
            if (metadata.average_reliability !== undefined) {
                // Could add display for average reliability
            }
        }

        function animateCounters() {
            const counters = document.querySelectorAll('.animated-counter');
            counters.forEach((counter, index) => {
                setTimeout(() => {
                    counter.classList.add('counter-up');
                }, index * 50);
            });
        }

        function updateStats(opportunities) {
            document.getElementById('total-opportunities').textContent = opportunities.length;

            if (opportunities.length > 0) {
                const avgProfit = opportunities.reduce((sum, op) => sum + op.profit_percentage, 0) / opportunities.length;
                document.getElementById('average-profit').textContent = avgProfit.toFixed(2) + '%';
            } else {
                document.getElementById('average-profit').textContent = '0%';
            }
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(0);
        }

        function showDetail(symbol, opportunity) {
            const modal = document.getElementById('detail-modal');
            const content = document.getElementById('modal-content');

            // Enhanced detail view dengan DEX support
            let priceSection = `
                <div>
                    <h3>💰 Informasi Harga</h3>
                    <p><strong>Binance:</strong> $${opportunity.binance_price.toFixed(6)}</p>
                    <p><strong>Bybit:</strong> $${opportunity.bybit_price.toFixed(6)}</p>
                    <p><strong>Profit Kotor:</strong> <span class="profit-positive">${opportunity.profit_percentage.toFixed(3)}%</span></p>
                    <p><strong>Profit Bersih:</strong> <span class="profit-positive">${opportunity.net_profit.toFixed(3)}%</span></p>
                    <p><strong>Type:</strong> <span class="arbitrage-type-badge arbitrage-${opportunity.arbitrage_type.toLowerCase().replace('-', '-')}">${opportunity.arbitrage_type}</span></p>
                </div>
            `;

            let volumeSection = `
                <div>
                    <h3>📊 Volume & Likuiditas</h3>
                    <p><strong>Volume 24h:</strong> $${formatNumber(opportunity.volume_24h)}</p>
                    <p><strong>Volume Binance:</strong> $${formatNumber(opportunity.binance_volume)}</p>
                    <p><strong>Volume Bybit:</strong> $${formatNumber(opportunity.bybit_volume)}</p>
                    <p><strong>Arah Trading:</strong> ${getDirectionText(opportunity)}</p>
                    <p><strong>Reliability Score:</strong> <span class="profit-positive">${(opportunity.reliability_score || 0).toFixed(1)}/100</span></p>
                    <p><strong>Quality Grade:</strong> <span class="grade-badge ${getGradeClass(opportunity.quality_grade || 'C')}">${opportunity.quality_grade || 'C'}</span></p>
                </div>
            `;

            // Add DEX-specific information
            let dexSection = '';
            if (opportunity.arbitrage_type === 'DEX-CEX') {
                dexSection = `
                    <div style="grid-column: 1 / -1; margin-top: 20px;">
                        <h3>🔄 Informasi DEX</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <p><strong>DEX Platform:</strong> <span class="dex-platform-icon dex-${(opportunity.dex_name || '').toLowerCase()}">${opportunity.dex_name || 'Unknown'}</span></p>
                                <p><strong>DEX Liquidity:</strong> $${formatNumber(opportunity.dex_liquidity_usd || 0)}</p>
                                <p><strong>DEX Volume 24h:</strong> $${formatNumber(opportunity.dex_volume_24h || 0)}</p>
                                <p><strong>Price Impact:</strong> <span class="profit-positive">${(opportunity.price_impact_estimate || 0).toFixed(2)}%</span></p>
                            </div>
                            <div>
                                <p><strong>Token Address:</strong> <code style="font-size: 0.8em;">${(opportunity.token_mint_address || 'N/A').substring(0, 20)}...</code></p>
                                <p><strong>Pair Address:</strong> <code style="font-size: 0.8em;">${(opportunity.dex_pair_address || 'N/A').substring(0, 20)}...</code></p>
                                <p><strong>Gas Fee Estimate:</strong> $${(opportunity.gas_fee_estimate || 0).toFixed(4)}</p>
                                <p><strong>DEX Trades 24h:</strong> ${opportunity.dex_trade_count_24h || 0}</p>
                            </div>
                        </div>
                    </div>
                `;
            }

            content.innerHTML = `
                <h2>📊 Detail Peluang: ${symbol}</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    ${priceSection}
                    ${volumeSection}
                    ${dexSection}
                </div>

                <h3>🌐 Jaringan Transfer Tersedia</h3>
                <div style="display: flex; gap: 10px; margin: 10px 0;">
                    ${opportunity.networks.map(network => `<span class="direction-badge direction-binance">${network}</span>`).join('')}
                </div>

                <h3>🧮 Kalkulator Profit</h3>
                <div style="margin: 15px 0;">
                    <label>Jumlah Investasi ($):</label>
                    <input type="number" id="investment-amount" value="1000" min="10" max="10000" style="width: 100%; padding: 10px; margin: 5px 0; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 5px; color: white;">
                    <div id="profit-calculation" style="margin-top: 10px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 10px;">
                        <p><strong>Profit Estimasi:</strong> $<span id="estimated-profit">0</span></p>
                        <p><strong>ROI:</strong> <span id="estimated-roi">0</span>%</p>
                    </div>
                </div>

                <div style="display: flex; gap: 15px; margin-top: 20px;">
                    <a href="${opportunity.binance_link}" target="_blank" class="btn" style="flex: 1;">🔗 Trade di Binance</a>
                    <a href="${opportunity.bybit_link}" target="_blank" class="btn" style="flex: 1;">🔗 Trade di Bybit</a>
                </div>
            `;

            modal.style.display = 'block';

            // Setup profit calculator
            const investmentInput = document.getElementById('investment-amount');
            investmentInput.addEventListener('input', function() {
                const amount = parseFloat(this.value) || 0;
                const profit = amount * (opportunity.net_profit / 100);
                document.getElementById('estimated-profit').textContent = profit.toFixed(2);
                document.getElementById('estimated-roi').textContent = opportunity.net_profit.toFixed(2);
            });

            // Trigger initial calculation
            investmentInput.dispatchEvent(new Event('input'));
        }

        function getDirectionText(opportunity) {
            if (opportunity.arbitrage_type === 'DEX-CEX') {
                if (opportunity.direction === 'cex_to_dex') {
                    return `CEX → ${opportunity.dex_name || 'DEX'}`;
                } else {
                    return `${opportunity.dex_name || 'DEX'} → CEX`;
                }
            } else {
                return opportunity.direction === 'binance_to_bybit' ? 'Binance → Bybit' : 'Bybit → Binance';
            }
        }

        function closeModal() {
            document.getElementById('detail-modal').style.display = 'none';
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast show';

            if (type === 'error') {
                toast.style.background = 'rgba(255, 71, 87, 0.9)';
            } else {
                toast.style.background = 'rgba(0, 255, 136, 0.9)';
            }

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function toggleAutoScan() {
            autoScanEnabled = !autoScanEnabled;
            const toggle = document.getElementById('auto-scan-toggle');

            if (autoScanEnabled) {
                toggle.classList.add('active');
                startAutoScan();
                showToast('✅ Auto scan diaktifkan');
            } else {
                toggle.classList.remove('active');
                stopAutoScan();
                showToast('⏸️ Auto scan dinonaktifkan');
            }
        }

        function startAutoScan() {
            if (autoScanEnabled) {
                scanInterval = setInterval(loadOpportunities, 30000); // 30 detik
            }
        }

        function stopAutoScan() {
            if (scanInterval) {
                clearInterval(scanInterval);
            }
        }

        async function triggerManualScan() {
            showToast('🔄 Memulai scan manual...');

            try {
                const response = await fetch('/api/scan');
                const data = await response.json();

                if (data.success) {
                    showToast(`✅ Scan selesai: ${data.opportunities_found} peluang ditemukan`);
                    loadOpportunities();
                } else {
                    showToast('❌ Scan gagal: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('❌ Error saat scan: ' + error.message, 'error');
            }
        }

        function applyFilters() {
            currentFilters.min_profit = parseFloat(document.getElementById('min-profit').value);
            currentFilters.max_profit = parseFloat(document.getElementById('max-profit').value);
            currentFilters.min_volume = parseFloat(document.getElementById('min-volume').value);
            currentFilters.network = document.getElementById('network-filter').value;

            loadOpportunities();
            showToast('🎛️ Filter diterapkan');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('detail-modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
                event.preventDefault();
                triggerManualScan();
            }
        });
    </script>
</body>
</html>
"""

# Background Scanner Thread
class BackgroundScanner:
    """Background scanner untuk auto-update peluang"""
    def __init__(self, engine: ArbitrageEngine):
        self.engine = engine
        self.running = False
        self.thread = None

    def start(self):
        """Start background scanning"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._scan_loop, daemon=True)
            self.thread.start()
            logger.info("Background scanner started")

    def stop(self):
        """Stop background scanning"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("Background scanner stopped")

    def _scan_loop(self):
        """Main scanning loop"""
        while self.running:
            try:
                # Scan opportunities
                opportunities = self.engine.scan_opportunities()

                # Log high profit opportunities
                high_profit_ops = [op for op in opportunities if op.profit_percentage > 5]
                if high_profit_ops:
                    logger.info(f"[ALERT] {len(high_profit_ops)} high profit opportunities found!")
                    for op in high_profit_ops[:3]:  # Log top 3
                        logger.info(f"   {op.symbol}: {op.profit_percentage:.2f}% profit")

                # Wait 30 seconds before next scan
                for _ in range(30):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Background scan error: {e}")
                time.sleep(10)  # Wait 10 seconds on error

def setup_logging():
    """Setup enhanced logging"""
    # Create logs directory if not exists
    import os
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Configure logging with rotation
    from logging.handlers import RotatingFileHandler

    # File handler with rotation
    file_handler = RotatingFileHandler(
        'logs/arbitrage.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.handlers.clear()
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def print_startup_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     🚀 CryptoArb Pro v2.0                    ║
    ║              Deteksi Arbitrase Cryptocurrency                ║
    ║                    Binance vs Bybit                          ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📊 Target: 300+ trading pairs                              ║
    ║  ⚡ Performance: 20+ tokens/second, <2GB memory             ║
    ║  🔄 Update: Real-time setiap 30 detik                       ║
    ║  💰 Profit Range: 0.5% - 200%                               ║
    ║  🌐 UI: Dark Futuristic Theme dengan Glassmorphism          ║
    ║  🔗 API: Binance + Bybit (Public endpoints only)            ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  Author: BOBACHEESE                                          ║
    ║  Framework: Flask + HTML/CSS/JavaScript                     ║
    ║  Environment: Conda on Windows 11                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def validate_environment():
    """Validate environment and dependencies"""
    try:
        # Test imports
        requests.get  # Test requests module
        Flask  # Test flask module
        logger.info("[OK] All dependencies available")
        return True
    except (ImportError, AttributeError) as e:
        logger.error(f"[ERROR] Missing dependency: {e}")
        return False

def test_api_connections():
    """Test API connections"""
    logger.info("[INFO] Testing API connections...")

    # Test Binance
    try:
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
        if response.status_code == 200:
            logger.info("[OK] Binance API connection successful")
        else:
            logger.warning(f"[WARN] Binance API returned status {response.status_code}")
    except Exception as e:
        logger.error(f"[ERROR] Binance API connection failed: {e}")

    # Test Bybit
    try:
        response = requests.get("https://api.bybit.com/v5/market/time", timeout=5)
        if response.status_code == 200:
            logger.info("[OK] Bybit API connection successful")
        else:
            logger.warning(f"[WARN] Bybit API returned status {response.status_code}")
    except Exception as e:
        logger.error(f"[ERROR] Bybit API connection failed: {e}")

if __name__ == '__main__':
    # Setup
    setup_logging()
    print_startup_banner()

    # Validate environment
    if not validate_environment():
        logger.error("[ERROR] Environment validation failed. Please install required dependencies.")
        exit(1)

    # Test API connections
    test_api_connections()

    # Initialize background scanner
    background_scanner = BackgroundScanner(arbitrage_engine)

    try:
        logger.info("[START] Starting CryptoArb Pro...")

        # Start background scanner
        background_scanner.start()

        # Initial scan
        logger.info("[SCAN] Performing initial scan...")
        initial_opportunities = arbitrage_engine.scan_opportunities()
        logger.info(f"[RESULT] Initial scan found {len(initial_opportunities)} opportunities")

        # Start Flask app
        logger.info("[WEB] Starting web interface...")
        logger.info("[INFO] Access the application at: http://localhost:5000")
        logger.info("[INFO] Use Ctrl+C to stop the application")

        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False  # Disable reloader to prevent duplicate background threads
        )

    except KeyboardInterrupt:
        logger.info("[STOP] Shutdown signal received...")
    except Exception as e:
        logger.error(f"[ERROR] Application error: {e}")
    finally:
        # Cleanup
        logger.info("[CLEANUP] Cleaning up...")
        background_scanner.stop()
        logger.info("[OK] CryptoArb Pro stopped successfully")
        print("\n[THANKS] Terima kasih telah menggunakan CryptoArb Pro!")
        print("[SUPPORT] Untuk support: https://github.com/bobacheese")
