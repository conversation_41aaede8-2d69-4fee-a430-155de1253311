<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Manual Analysis Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .analysis-control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .control-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .control-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .control-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-text {
            color: #00ffff;
            font-weight: 500;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #666;
        }
        
        .status-indicator.idle {
            background: #00ffff;
            box-shadow: 0 0 10px #00ffff;
        }
        
        .status-indicator.running {
            background: #ffff00;
            box-shadow: 0 0 10px #ffff00;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.completed {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }
        
        .status-indicator.error {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }
        
        .start-analysis-btn {
            background: linear-gradient(135deg, #00ffff, #8a2be2);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: #ffffff;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 160px;
            justify-content: center;
        }
        
        .start-analysis-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        }
        
        .start-analysis-btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .start-analysis-btn.analyzing {
            background: linear-gradient(135deg, #ffff00, #ff0000);
        }
        
        .start-analysis-btn.completed {
            background: linear-gradient(135deg, #00ff00, #00ffff);
        }
        
        .progress-container {
            margin-top: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            display: none;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #8a2be2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .log {
            margin: 5px 0;
            padding: 5px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log.error { color: #ff4444; }
        .log.success { color: #44ff44; }
        .log.info { color: #4444ff; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Minimal Manual Analysis Test</h1>
        
        <!-- Manual Analysis Control Panel -->
        <div class="analysis-control-panel" id="analysis-control-panel">
            <div class="control-header">
                <h3 class="control-title">🚀 Kontrol Analisis Manual</h3>
                <div class="control-status" id="control-status">
                    <span class="status-text">Memuat sistem analisis...</span>
                    <div class="status-indicator idle" id="analysis-status-indicator"></div>
                </div>
            </div>

            <div class="control-actions">
                <button class="start-analysis-btn" id="start-analysis-btn" disabled>
                    <span class="btn-icon">⏳</span>
                    <span class="btn-text">Memuat...</span>
                </button>
            </div>

            <!-- Progress Bar (hidden by default) -->
            <div class="progress-container" id="progress-container">
                <div class="progress-header">
                    <span class="progress-text" id="progress-text">Menganalisis...</span>
                    <span class="progress-percentage" id="progress-percentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-details">
                    <span class="current-pair" id="current-pair">-</span>
                    <span class="estimated-time" id="estimated-time">-</span>
                </div>
            </div>
        </div>
        
        <div id="log-container">
            <h3>📋 Debug Log</h3>
        </div>
    </div>

    <script>
        console.log('[MINIMAL] Script loaded');
        
        const logContainer = document.getElementById('log-container');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(div);
            console.log(`[MINIMAL] ${message}`);
        }
        
        // Minimal Manual Analysis Implementation
        class MinimalManualAnalysis {
            constructor() {
                this.isRunning = false;
                this.progressInterval = null;
                
                log('MinimalManualAnalysis initialized', 'success');
                this.init();
            }
            
            init() {
                log('Initializing minimal manual analysis...', 'info');
                
                // Setup event listeners
                this.setupEventListeners();
                
                // Initialize UI
                setTimeout(() => {
                    this.updateAnalysisUI('idle');
                    log('UI initialized to idle state', 'success');
                }, 1000);
            }
            
            setupEventListeners() {
                const btn = document.getElementById('start-analysis-btn');
                if (btn) {
                    btn.addEventListener('click', () => {
                        log('Start button clicked', 'info');
                        this.startManualAnalysis();
                    });
                    log('Event listener attached to start button', 'success');
                } else {
                    log('Start button not found', 'error');
                }
            }
            
            updateAnalysisUI(state) {
                log(`Updating UI to state: ${state}`, 'info');
                
                const btn = document.getElementById('start-analysis-btn');
                const statusText = document.querySelector('.status-text');
                const statusIndicator = document.getElementById('analysis-status-indicator');
                const progressContainer = document.getElementById('progress-container');

                if (!btn || !statusText || !statusIndicator) {
                    log('Required UI elements not found', 'error');
                    return;
                }

                // Reset classes
                btn.className = 'start-analysis-btn';
                statusIndicator.className = 'status-indicator';

                switch (state) {
                    case 'idle':
                        btn.innerHTML = '<span class="btn-icon">▶️</span><span class="btn-text">Mulai Analisis</span>';
                        btn.disabled = false;
                        statusText.textContent = 'Siap untuk analisis';
                        statusIndicator.classList.add('idle');
                        if (progressContainer) progressContainer.style.display = 'none';
                        log('UI updated to idle state', 'success');
                        break;

                    case 'running':
                        btn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">Sedang Analisis...</span>';
                        btn.disabled = true;
                        btn.classList.add('analyzing');
                        statusText.textContent = 'Analisis sedang berjalan';
                        statusIndicator.classList.add('running');
                        if (progressContainer) progressContainer.style.display = 'block';
                        log('UI updated to running state', 'success');
                        break;

                    case 'completed':
                        btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Analisis Ulang</span>';
                        btn.disabled = false;
                        btn.classList.add('completed');
                        statusText.textContent = 'Analisis selesai';
                        statusIndicator.classList.add('completed');
                        if (progressContainer) progressContainer.style.display = 'none';
                        log('UI updated to completed state', 'success');
                        break;

                    case 'error':
                        btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Coba Lagi</span>';
                        btn.disabled = false;
                        statusText.textContent = 'Analisis gagal';
                        statusIndicator.classList.add('error');
                        if (progressContainer) progressContainer.style.display = 'none';
                        log('UI updated to error state', 'success');
                        break;
                }
            }
            
            async startManualAnalysis() {
                try {
                    log('Starting manual analysis...', 'info');
                    
                    this.updateAnalysisUI('running');
                    
                    const response = await fetch('/api/start-analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        log(`Analysis started: ${data.message}`, 'success');
                        this.startProgressMonitoring();
                    } else {
                        throw new Error(data.error || 'Failed to start analysis');
                    }

                } catch (error) {
                    log(`Error starting analysis: ${error.message}`, 'error');
                    this.updateAnalysisUI('error');
                }
            }
            
            startProgressMonitoring() {
                log('Starting progress monitoring...', 'info');
                
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                }

                this.progressInterval = setInterval(async () => {
                    try {
                        const response = await fetch('/api/analysis-progress');
                        const data = await response.json();

                        if (data.success) {
                            this.updateProgressUI(data.progress);
                            
                            if (!data.progress.is_running) {
                                clearInterval(this.progressInterval);
                                this.progressInterval = null;
                                
                                if (data.progress.error_message) {
                                    this.updateAnalysisUI('error');
                                    log(`Analysis failed: ${data.progress.error_message}`, 'error');
                                } else {
                                    this.updateAnalysisUI('completed');
                                    log('Analysis completed successfully!', 'success');
                                }
                            }
                        }
                    } catch (error) {
                        log(`Error monitoring progress: ${error.message}`, 'error');
                    }
                }, 2000);
            }
            
            updateProgressUI(progress) {
                const progressText = document.getElementById('progress-text');
                const progressPercentage = document.getElementById('progress-percentage');
                const progressFill = document.getElementById('progress-fill');

                if (!progress.is_running) return;

                const percentage = progress.total_pairs > 0 ? 
                    Math.round((progress.progress / progress.total_pairs) * 100) : 0;

                if (progressText) {
                    progressText.textContent = `Menganalisis... ${progress.progress}/${progress.total_pairs} pairs`;
                }

                if (progressPercentage) {
                    progressPercentage.textContent = `${percentage}%`;
                }

                if (progressFill) {
                    progressFill.style.width = `${percentage}%`;
                }
                
                log(`Progress: ${percentage}% (${progress.progress}/${progress.total_pairs})`, 'info');
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            log('DOM loaded, initializing minimal manual analysis...', 'info');
            window.minimalApp = new MinimalManualAnalysis();
        });
        
        // Also initialize if DOM is already ready
        if (document.readyState !== 'loading') {
            log('DOM already ready, initializing minimal manual analysis...', 'info');
            window.minimalApp = new MinimalManualAnalysis();
        }
        
        log('Minimal test script loaded successfully', 'success');
        
    </script>
</body>
</html>
