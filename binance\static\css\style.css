/* Binance Signal Generator Pro - Dark Futuristic Theme */
/* Consistent dengan existing crypto arbitrage bot */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Consistent dengan existing bot */
    --primary-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    --card-bg: rgba(255, 255, 255, 0.1);
    --card-bg-hover: rgba(255, 255, 255, 0.15);
    --accent-cyan: #00ffff;
    --accent-purple: #8b5cf6;
    --accent-green: #10b981;
    --accent-red: #ef4444;
    --accent-yellow: #f59e0b;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #6b7280;
    --border-color: rgba(255, 255, 255, 0.2);
    --border-color-hover: rgba(255, 255, 255, 0.3);
    --shadow-glow: 0 8px 32px rgba(0, 255, 255, 0.1);
    --shadow-glow-purple: 0 8px 32px rgba(139, 92, 246, 0.1);
    --shadow-glow-green: 0 8px 32px rgba(16, 185, 129, 0.1);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;
    
    /* Spacing */
    --spacing-xs: 8px;
    --spacing-sm: 12px;
    --spacing-md: 20px;
    --spacing-lg: 30px;
    --spacing-xl: 40px;
    
    /* Border Radius */
    --border-radius: 15px;
    --border-radius-sm: 8px;
    --border-radius-lg: 20px;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.8s ease-out;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    animation: pulse 2s infinite;
}

.loading-text {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 255, 0.3);
    border-top: 3px solid var(--accent-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-status {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-md);
    align-items: center;
    box-shadow: var(--shadow-glow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.logo-icon {
    font-size: var(--font-size-2xl);
    animation: pulse 2s infinite;
}

.logo-text {
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.status-indicators {
    display: flex;
    gap: var(--spacing-md);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    transition: var(--transition-smooth);
}

.status-dot.healthy {
    background: var(--accent-green);
    box-shadow: 0 0 10px var(--accent-green);
}

.status-dot.error {
    background: var(--accent-red);
    box-shadow: 0 0 10px var(--accent-red);
}

.status-dot.loading {
    background: var(--accent-yellow);
    box-shadow: 0 0 10px var(--accent-yellow);
    animation: pulse 1s infinite;
}

.header-center {
    display: flex;
    justify-content: center;
}

.market-stats {
    display: flex;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--accent-cyan);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.current-time {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.author-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: var(--font-size-sm);
}

.author-btn:hover {
    background: var(--card-bg-hover);
    border-color: var(--border-color-hover);
    box-shadow: var(--shadow-glow-purple);
    transform: translateY(-2px);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: var(--font-size-sm);
    font-weight: 500;
    flex: 1;
    justify-content: center;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--accent-cyan);
    color: #000;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.tab-icon {
    font-size: var(--font-size-base);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.tab-content {
    display: none;
    flex: 1;
    flex-direction: column;
}

.tab-content.active {
    display: flex;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.content-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 20px currentColor;
    }
}

/* Form Controls */
.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    white-space: nowrap;
}

.filter-select,
.search-input,
.setting-input {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-smooth);
    min-width: 120px;
}

.filter-select:focus,
.search-input:focus,
.setting-input:focus {
    outline: none;
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.filter-range {
    width: 100px;
    height: 4px;
    background: var(--card-bg);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
}

.filter-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--accent-cyan);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.filter-range::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--accent-cyan);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Buttons */
.refresh-btn,
.search-btn,
.setting-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--accent-cyan);
    color: #000;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.refresh-btn:hover,
.search-btn:hover,
.setting-btn:hover {
    background: var(--accent-purple);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple);
}

.refresh-btn:active,
.search-btn:active,
.setting-btn:active {
    transform: translateY(0);
}

/* Search Group */
.search-group {
    display: flex;
    gap: var(--spacing-xs);
}

.search-input {
    min-width: 200px;
}

/* Signals Grid */
.signals-container {
    flex: 1;
    overflow-y: auto;
}

.signals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-xs);
}

.signal-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.signal-card:hover {
    background: var(--card-bg-hover);
    border-color: var(--border-color-hover);
    transform: translateY(-4px);
    box-shadow: var(--shadow-glow);
}

.signal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.signal-symbol {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.signal-type {
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.signal-type.strong-buy {
    background: var(--accent-green);
    color: #000;
}

.signal-type.buy {
    background: rgba(16, 185, 129, 0.3);
    color: var(--accent-green);
}

.signal-type.hold {
    background: rgba(245, 158, 11, 0.3);
    color: var(--accent-yellow);
}

.signal-type.sell {
    background: rgba(239, 68, 68, 0.3);
    color: var(--accent-red);
}

.signal-type.strong-sell {
    background: var(--accent-red);
    color: #000;
}

.signal-confidence {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.confidence-bar {
    flex: 1;
    height: 6px;
    background: var(--card-bg);
    border-radius: 3px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-yellow), var(--accent-green));
    border-radius: 3px;
    transition: width 0.5s ease-out;
}

.confidence-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.signal-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: 2px;
}

.detail-value {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.signal-reasoning {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
}

.signal-ai-analysis {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
    padding: var(--spacing-xs);
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: var(--border-radius-sm);
    white-space: pre-wrap;
}

.signal-timestamp {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-align: right;
    margin-top: var(--spacing-xs);
}

/* Analysis Result */
.analysis-container {
    flex: 1;
    overflow-y: auto;
}

.analysis-result {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state {
    text-align: center;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-text {
    font-size: var(--font-size-lg);
}

/* Chart Container */
.chart-container {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    height: 500px;
    position: relative;
}

.chart-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* Settings */
.settings-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.settings-section {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.settings-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--accent-cyan);
}

.setting-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.setting-item label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    min-width: 150px;
}

.setting-checkbox {
    width: 20px;
    height: 20px;
    accent-color: var(--accent-cyan);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideInUp 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--accent-cyan);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    transition: var(--transition-smooth);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--spacing-lg);
}

.author-info {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.author-avatar {
    font-size: 4rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    border: 2px solid var(--accent-cyan);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.author-details {
    flex: 1;
}

.author-name {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.author-title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.author-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.author-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-smooth);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
}

.author-link:hover {
    color: var(--accent-cyan);
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(5px);
}

.link-icon {
    font-size: var(--font-size-base);
}

.link-text {
    font-size: var(--font-size-sm);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.notification {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.notification.success::before {
    background: var(--accent-green);
}

.notification.error::before {
    background: var(--accent-red);
}

.notification.warning::before {
    background: var(--accent-yellow);
}

.notification.info::before {
    background: var(--accent-cyan);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.notification-icon {
    font-size: var(--font-size-lg);
    margin-top: 2px;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text-primary);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--font-size-lg);
    padding: 0;
    margin-left: var(--spacing-sm);
    transition: var(--transition-smooth);
}

.notification-close:hover {
    color: var(--text-primary);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading-shimmer 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .signals-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }

    .market-stats {
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm);
    }

    .header {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .header-left,
    .header-center,
    .header-right {
        justify-content: center;
    }

    .market-stats {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        flex: 1;
        min-width: 120px;
    }

    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .content-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .signals-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .signal-details {
        grid-template-columns: 1fr;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
    }

    .chart-container {
        height: 300px;
    }

    .setting-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .setting-item label {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--spacing-xs);
    }

    .header {
        padding: var(--spacing-sm);
    }

    .logo-text {
        display: none;
    }

    .status-indicators {
        gap: var(--spacing-sm);
    }

    .nav-tab {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }

    .tab-text {
        display: none;
    }

    .content-title {
        font-size: var(--font-size-xl);
    }

    .signal-card {
        padding: var(--spacing-sm);
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-sm);
    }

    .notification {
        min-width: 250px;
        margin: var(--spacing-xs);
    }
}

/* Additional Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--card-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-cyan);
    border-radius: 4px;
    transition: var(--transition-smooth);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-purple);
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .status-dot {
        width: 12px;
        height: 12px;
    }

    .loading-spinner {
        border-width: 4px;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow-glow: 0 8px 32px rgba(0, 255, 255, 0.15);
        --shadow-glow-purple: 0 8px 32px rgba(139, 92, 246, 0.15);
        --shadow-glow-green: 0 8px 32px rgba(16, 185, 129, 0.15);
    }
}

/* Print Styles */
@media print {
    .header,
    .nav-tabs,
    .content-controls,
    .modal,
    .notification-container {
        display: none !important;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .signal-card {
        break-inside: avoid;
        margin-bottom: var(--spacing-md);
    }
}

/* Analysis Control Panel */
.analysis-control-panel {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-glow);
    transition: var(--transition-smooth);
}

.analysis-control-panel:hover {
    border-color: var(--border-color-hover);
    box-shadow: var(--shadow-glow-purple);
}

.control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.control-title {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-text {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

#analysis-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: var(--transition-smooth);
}

#analysis-status-indicator.idle {
    background: var(--accent-yellow);
    box-shadow: 0 0 10px var(--accent-yellow);
}

#analysis-status-indicator.running {
    background: var(--accent-cyan);
    box-shadow: 0 0 10px var(--accent-cyan);
    animation: pulse 2s infinite;
}

#analysis-status-indicator.completed {
    background: var(--accent-green);
    box-shadow: 0 0 10px var(--accent-green);
}

#analysis-status-indicator.error {
    background: var(--accent-red);
    box-shadow: 0 0 10px var(--accent-red);
}

.control-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.start-analysis-btn {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-glow);
    min-width: 160px;
    justify-content: center;
}

.start-analysis-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple);
}

.start-analysis-btn:active {
    transform: translateY(0);
}

.start-analysis-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.start-analysis-btn.analyzing {
    background: linear-gradient(135deg, var(--accent-yellow), var(--accent-red));
}

.start-analysis-btn.completed {
    background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan));
}

.analysis-info {
    display: flex;
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* Progress Container */
.progress-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.progress-text {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.progress-percentage {
    color: var(--accent-cyan);
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
    border-radius: 4px;
    transition: width 0.3s ease-out;
    width: 0%;
    box-shadow: 0 0 10px var(--accent-cyan);
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-pair {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    font-family: 'Courier New', monospace;
}

.estimated-time {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* Pulse animation for running indicator */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}
