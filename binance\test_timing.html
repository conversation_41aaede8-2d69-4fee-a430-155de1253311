<!DOCTYPE html>
<html>
<head>
    <title>Timing Test</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .log { margin: 2px 0; font-size: 12px; }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
    </style>
</head>
<body>
    <h1>Manual Analysis Timing Test</h1>
    <div id="console-output"></div>
    
    <script>
        const output = document.getElementById('console-output');
        let checkCount = 0;
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        function checkElements() {
            checkCount++;
            log(`=== Check #${checkCount} ===`, 'info');
            
            // Check if we're on the main page
            const isMainPage = window.location.pathname === '/';
            log(`On main page: ${isMainPage}`, isMainPage ? 'success' : 'warn');
            
            if (!isMainPage) {
                log('Not on main page, redirecting...', 'warn');
                window.location.href = '/';
                return;
            }
            
            // Check loading screen
            const loadingScreen = document.getElementById('loading-screen');
            const mainContainer = document.getElementById('main-container');
            
            log(`Loading screen exists: ${!!loadingScreen}`, loadingScreen ? 'info' : 'error');
            log(`Main container exists: ${!!mainContainer}`, mainContainer ? 'info' : 'error');
            
            if (loadingScreen) {
                const loadingDisplay = window.getComputedStyle(loadingScreen).display;
                log(`Loading screen display: ${loadingDisplay}`, loadingDisplay === 'none' ? 'success' : 'warn');
            }
            
            if (mainContainer) {
                const mainDisplay = window.getComputedStyle(mainContainer).display;
                log(`Main container display: ${mainDisplay}`, mainDisplay !== 'none' ? 'success' : 'warn');
            }
            
            // Check manual analysis elements
            const btn = document.getElementById('start-analysis-btn');
            const statusText = document.querySelector('.status-text');
            const statusIndicator = document.getElementById('analysis-status-indicator');
            const progressContainer = document.getElementById('progress-container');
            
            log(`Start button exists: ${!!btn}`, btn ? 'success' : 'error');
            log(`Status text exists: ${!!statusText}`, statusText ? 'success' : 'error');
            log(`Status indicator exists: ${!!statusIndicator}`, statusIndicator ? 'success' : 'error');
            log(`Progress container exists: ${!!progressContainer}`, progressContainer ? 'success' : 'error');
            
            if (btn) {
                log(`Button innerHTML: "${btn.innerHTML}"`, 'info');
                log(`Button disabled: ${btn.disabled}`, 'info');
                log(`Button className: "${btn.className}"`, 'info');
                log(`Button style.display: "${btn.style.display}"`, 'info');
                
                const btnDisplay = window.getComputedStyle(btn).display;
                log(`Button computed display: "${btnDisplay}"`, btnDisplay !== 'none' ? 'success' : 'error');
                
                const btnVisibility = window.getComputedStyle(btn).visibility;
                log(`Button visibility: "${btnVisibility}"`, btnVisibility !== 'hidden' ? 'success' : 'error');
            }
            
            if (statusText) {
                log(`Status text content: "${statusText.textContent}"`, 'info');
            }
            
            if (statusIndicator) {
                log(`Status indicator className: "${statusIndicator.className}"`, 'info');
            }
            
            // Check if BinanceSignalApp exists
            if (window.app) {
                log(`BinanceSignalApp instance exists: true`, 'success');
                log(`App loadingComplete: ${window.app.loadingComplete}`, 'info');
            } else {
                log(`BinanceSignalApp instance exists: false`, 'error');
            }
            
            log('--- End Check ---', 'info');
        }
        
        // Check immediately
        log('Starting timing test...', 'info');
        checkElements();
        
        // Check every 2 seconds for 30 seconds
        const interval = setInterval(() => {
            checkElements();
            
            if (checkCount >= 15) {
                clearInterval(interval);
                log('=== TIMING TEST COMPLETED ===', 'success');
            }
        }, 2000);
        
        // Also check when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                log('DOMContentLoaded event fired', 'success');
                setTimeout(checkElements, 100);
            });
        } else {
            log('DOM already ready', 'success');
        }
        
        // Check when window loads
        window.addEventListener('load', () => {
            log('Window load event fired', 'success');
            setTimeout(checkElements, 100);
        });
        
    </script>
</body>
</html>
