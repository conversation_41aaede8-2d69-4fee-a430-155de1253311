#!/usr/bin/env python3
"""
Test script untuk API manual analysis
"""

import requests
import json
import time

def test_start_analysis():
    """Test endpoint start-analysis"""
    try:
        print("Testing /api/start-analysis...")
        response = requests.post('http://localhost:5001/api/start-analysis', 
                               headers={'Content-Type': 'application/json'})
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.json()
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_analysis_progress():
    """Test endpoint analysis-progress"""
    try:
        print("\nTesting /api/analysis-progress...")
        response = requests.get('http://localhost:5001/api/analysis-progress')
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.json()
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_status():
    """Test endpoint status"""
    try:
        print("\nTesting /api/status...")
        response = requests.get('http://localhost:5001/api/status')
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.json()
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    print("=== Testing Binance Signal Generator API ===")
    
    # Test status first
    test_status()
    
    # Test start analysis
    result = test_start_analysis()
    
    if result and result.get('success'):
        print("\n=== Monitoring Progress ===")
        # Monitor progress for 30 seconds
        for i in range(6):
            time.sleep(5)
            progress = test_analysis_progress()
            if progress and not progress.get('progress', {}).get('is_running', True):
                print("Analysis completed!")
                break
    
    print("\n=== Test completed ===")
