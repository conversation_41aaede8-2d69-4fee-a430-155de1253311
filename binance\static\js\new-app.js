/**
 * Binance Signal Generator Pro - New Clean Implementation
 * Modern, responsive UI with pastel design and smooth animations
 */

class BinanceSignalApp {
    constructor() {
        this.isAnalysisRunning = false;
        this.progressInterval = null;
        this.signals = [];
        this.currentTab = 'signals';
        this.geminiApiKey = localStorage.getItem('gemini_api_key') || '';
        this.isGeminiConnected = false;
        this.currentChart = null;
        this.currentSymbol = '';
        this.currentTimeframe = '1h';
        this.aiOverlayData = null;
        this.isChartFullscreen = false;
        this.signalDisplayLimit = parseInt(localStorage.getItem('signal_display_limit')) || 10;
        this.isPositionOpen = false;
        this.positionData = null;

        console.log('[NEW APP] Initializing Binance Signal Generator Pro...');
        this.init();
    }
    
    async init() {
        try {
            console.log('[NEW APP] Starting initialization...');
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize UI components
            this.initializeUI();
            
            // Load initial data
            await this.loadInitialData();
            
            console.log('[NEW APP] Initialization completed successfully');
            
        } catch (error) {
            console.error('[NEW APP] Initialization failed:', error);
            this.showNotification('Failed to initialize application', 'error');
        }
    }
    
    setupEventListeners() {
        console.log('[NEW APP] Setting up event listeners...');
        
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                this.switchTab(tabId);
            });
        });
        
        // Manual analysis button
        const startBtn = document.getElementById('start-analysis-btn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                console.log('[NEW APP] Manual analysis button clicked');
                this.startManualAnalysis();
            });
        }
        
        // Refresh signals button
        const refreshBtn = document.getElementById('refresh-signals');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadSignals();
            });
        }

        // Gemini API configuration
        this.setupGeminiEventListeners();

        // AI Analysis buttons
        this.setupAIAnalysisListeners();

        // Chart event listeners
        this.setupChartEventListeners();

        // Configuration event listeners
        this.setupConfigurationListeners();

        // Trading simulator event listeners
        this.setupTradingSimulatorListeners();

        console.log('[NEW APP] Event listeners setup completed');
    }
    
    initializeUI() {
        console.log('[NEW APP] Initializing UI components...');
        
        // Initialize manual analysis UI
        this.updateAnalysisUI('idle');

        // Show initial tab
        this.switchTab('signals');

        // Add smooth animations
        this.addUIAnimations();

        // Initialize Gemini API UI
        this.initializeGeminiUI();

        console.log('[NEW APP] UI initialization completed');
    }
    
    addUIAnimations() {
        // Add hover effects to cards
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
        
        // Add click ripple effect to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createRippleEffect(e, btn);
            });
        });
    }
    
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    
    switchTab(tabId) {
        console.log(`[NEW APP] Switching to tab: ${tabId}`);
        
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        document.querySelector(`[data-tab="${tabId}"]`)?.classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        document.getElementById(`${tabId}-tab`)?.classList.add('active');
        
        this.currentTab = tabId;
        
        // Load tab-specific data
        if (tabId === 'signals') {
            this.loadSignals();
        }
    }
    
    async loadInitialData() {
        console.log('[NEW APP] Loading initial data...');
        
        try {
            // Load status
            await this.updateStatus();
            
            // Load signals
            await this.loadSignals();
            
            console.log('[NEW APP] Initial data loaded successfully');
            
        } catch (error) {
            console.warn('[NEW APP] Some data failed to load:', error);
            // Continue anyway - app should work with limited functionality
        }
    }
    
    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatusDisplay(data.status);
                return data;
            }
        } catch (error) {
            console.warn('[NEW APP] Failed to update status:', error);
        }
        return null;
    }
    
    updateStatusDisplay(status) {
        // Update status indicators in the UI
        const elements = {
            'binance-api-status': status.binance_api === 'healthy' ? '🟢' : '🔴',
            'ai-service-status': status.ai_service === 'available' ? '🟢' : '🟡',
            'total-pairs': status.stats?.total_analyzed || '-',
            'signals-generated': status.stats?.signals_generated || '-',
            'last-update': status.stats?.last_update ? new Date(status.stats.last_update).toLocaleTimeString() : '-'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    async loadSignals() {
        console.log('[NEW APP] Loading signals...');

        // Show loading overlay
        this.showSignalsLoading(true);

        try {
            // Get limit from localStorage
            const limit = this.signalDisplayLimit;
            const response = await fetch(`/api/signals?limit=${limit}`);
            const data = await response.json();
            
            if (data.success) {
                this.signals = data.data || [];
                this.renderSignals();
                this.updateSignalCounter(data.count, data.total_available);
                this.showNotification(`Loaded ${data.count} of ${data.total_available} signals`, 'success');
            } else {
                throw new Error(data.error || 'Failed to load signals');
            }

        } catch (error) {
            console.error('[NEW APP] Failed to load signals:', error);
            this.showNotification('Failed to load signals', 'error');
            this.renderEmptySignals();
        } finally {
            this.showSignalsLoading(false);
        }
    }
    
    renderSignals() {
        const container = document.getElementById('signals-container');
        if (!container) return;
        
        if (this.signals.length === 0) {
            this.renderEmptySignals();
            return;
        }
        
        container.innerHTML = this.signals.map(signal => this.createSignalCard(signal)).join('');
        
        // Add animation to new cards
        container.querySelectorAll('.signal-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in-up');
        });
    }
    
    createSignalCard(signal) {
        const confidenceColor = this.getConfidenceColor(signal.confidence);
        const signalTypeColor = this.getSignalTypeColor(signal.signal_type);
        
        return `
            <div class="signal-card card" data-symbol="${signal.symbol}">
                <div class="signal-header">
                    <div class="signal-symbol">${signal.symbol}</div>
                    <div class="signal-type ${signalTypeColor}">${signal.signal_type}</div>
                </div>
                <div class="signal-metrics">
                    <div class="metric">
                        <span class="metric-label">Confidence</span>
                        <span class="metric-value ${confidenceColor}">${signal.confidence}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Risk Level</span>
                        <span class="metric-value">${signal.risk_level}</span>
                    </div>
                </div>
                <div class="signal-prices">
                    <div class="price-item">
                        <span class="price-label">Entry</span>
                        <span class="price-value">${signal.entry_points?.[0]?.toFixed(6) || 'N/A'}</span>
                    </div>
                    <div class="price-item">
                        <span class="price-label">Stop Loss</span>
                        <span class="price-value">${signal.stop_loss?.toFixed(6) || 'N/A'}</span>
                    </div>
                    <div class="price-item">
                        <span class="price-label">Take Profit</span>
                        <span class="price-value">${signal.take_profit?.[0]?.toFixed(6) || 'N/A'}</span>
                    </div>
                </div>
                <div class="signal-reasoning">
                    ${signal.reasoning || 'No reasoning provided'}
                </div>
                <div class="signal-actions">
                    <button class="ai-analysis-btn" data-symbol="${signal.symbol}">🤖 Analisis AI + Trading Sim</button>
                </div>
                <div class="signal-timestamp">
                    ${new Date(signal.timestamp).toLocaleString()}
                </div>
            </div>
        `;
    }
    
    renderEmptySignals() {
        const container = document.getElementById('signals-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <h3>No Signals Available</h3>
                <p>Start manual analysis to generate trading signals</p>
                <button class="btn btn-primary" onclick="app.startManualAnalysis()">
                    Start Analysis
                </button>
            </div>
        `;
    }
    
    getConfidenceColor(confidence) {
        if (confidence >= 80) return 'high-confidence';
        if (confidence >= 60) return 'medium-confidence';
        return 'low-confidence';
    }
    
    getSignalTypeColor(type) {
        const colors = {
            'STRONG_BUY': 'strong-buy',
            'BUY': 'buy',
            'HOLD': 'hold',
            'SELL': 'sell',
            'STRONG_SELL': 'strong-sell'
        };
        return colors[type] || 'neutral';
    }
    
    showNotification(message, type = 'info') {
        console.log(`[NEW APP] Notification: ${message} (${type})`);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
            </div>
        `;
        
        // Add to container
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }
    
    getNotificationIcon(type) {
        const icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        };
        return icons[type] || 'ℹ️';
    }

    // Manual Analysis Functions
    async startManualAnalysis() {
        if (this.isAnalysisRunning) {
            console.log('[NEW APP] Analysis already running');
            return;
        }

        try {
            console.log('[NEW APP] Starting manual analysis...');

            this.updateAnalysisUI('running');
            this.isAnalysisRunning = true;

            const response = await fetch('/api/start-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(data.message, 'success');
                this.startProgressMonitoring();
            } else {
                throw new Error(data.error || 'Failed to start analysis');
            }

        } catch (error) {
            console.error('[NEW APP] Error starting analysis:', error);
            this.showNotification(`Error starting analysis: ${error.message}`, 'error');
            this.updateAnalysisUI('error');
            this.isAnalysisRunning = false;
        }
    }

    startProgressMonitoring() {
        console.log('[NEW APP] Starting progress monitoring...');

        // Clear any existing interval
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Show progress container
        const progressContainer = document.getElementById('progress-container');
        if (progressContainer) {
            progressContainer.classList.add('show');
        }

        // Start monitoring
        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch('/api/analysis-progress');
                const data = await response.json();

                if (data.success) {
                    this.updateProgressUI(data.progress);

                    // Check if analysis is complete
                    if (!data.progress.is_running) {
                        this.stopProgressMonitoring();

                        if (data.progress.error_message) {
                            this.updateAnalysisUI('error');
                            this.showNotification(`Analysis failed: ${data.progress.error_message}`, 'error');
                        } else {
                            this.updateAnalysisUI('completed');
                            this.showNotification('Analysis completed successfully!', 'success');

                            // Reload signals after completion
                            setTimeout(() => {
                                this.loadSignals();
                            }, 1000);
                        }

                        this.isAnalysisRunning = false;
                    }
                }

            } catch (error) {
                console.error('[NEW APP] Error monitoring progress:', error);
            }
        }, 2000);
    }

    stopProgressMonitoring() {
        console.log('[NEW APP] Stopping progress monitoring...');

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        // Hide progress container
        const progressContainer = document.getElementById('progress-container');
        if (progressContainer) {
            progressContainer.classList.remove('show');
        }
    }

    updateAnalysisUI(state) {
        console.log(`[NEW APP] Updating analysis UI to state: ${state}`);

        const btn = document.getElementById('start-analysis-btn');
        const statusBadge = document.querySelector('.status-badge');

        if (!btn || !statusBadge) {
            console.warn('[NEW APP] Analysis UI elements not found');
            return;
        }

        // Reset classes
        btn.className = 'btn start-analysis-btn';
        statusBadge.className = 'status-badge';

        switch (state) {
            case 'idle':
                btn.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">Start Analysis</span>';
                btn.disabled = false;
                statusBadge.classList.add('idle');
                statusBadge.innerHTML = '<span class="status-indicator"></span>Ready for analysis';
                break;

            case 'running':
                btn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">Analyzing...</span>';
                btn.disabled = true;
                btn.classList.add('analyzing');
                statusBadge.classList.add('running');
                statusBadge.innerHTML = '<span class="status-indicator"></span>Analysis in progress';
                break;

            case 'completed':
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Analyze Again</span>';
                btn.disabled = false;
                btn.classList.add('completed');
                statusBadge.classList.add('completed');
                statusBadge.innerHTML = '<span class="status-indicator"></span>Analysis completed';
                break;

            case 'error':
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Try Again</span>';
                btn.disabled = false;
                btn.classList.add('error');
                statusBadge.classList.add('error');
                statusBadge.innerHTML = '<span class="status-indicator"></span>Analysis failed';
                break;
        }
    }

    updateProgressUI(progress) {
        if (!progress.is_running) return;

        const progressText = document.getElementById('progress-text');
        const progressPercentage = document.getElementById('progress-percentage');
        const progressFill = document.getElementById('progress-fill');
        const currentPair = document.getElementById('current-pair');
        const estimatedTime = document.getElementById('estimated-time');

        // Calculate percentage
        const percentage = progress.total_pairs > 0 ?
            Math.round((progress.progress / progress.total_pairs) * 100) : 0;

        // Update elements
        if (progressText) {
            progressText.textContent = `Analyzing ${progress.progress}/${progress.total_pairs} pairs`;
        }

        if (progressPercentage) {
            progressPercentage.textContent = `${percentage}%`;
        }

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (currentPair) {
            currentPair.textContent = progress.current_pair || '-';
        }

        if (estimatedTime && progress.estimated_completion) {
            const estimated = new Date(progress.estimated_completion);
            const now = new Date();
            const remaining = Math.max(0, Math.round((estimated - now) / 1000));

            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60);
                const seconds = remaining % 60;
                estimatedTime.textContent = `~${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
            } else {
                estimatedTime.textContent = 'Almost done...';
            }
        }
    }

    // Gemini AI Functions
    setupGeminiEventListeners() {
        console.log('[GEMINI] Setting up Gemini AI event listeners...');

        // API Key input
        const apiKeyInput = document.getElementById('gemini-api-key');
        if (apiKeyInput) {
            apiKeyInput.value = this.geminiApiKey;
            apiKeyInput.addEventListener('input', (e) => {
                this.geminiApiKey = e.target.value;
                localStorage.setItem('gemini_api_key', this.geminiApiKey);
                this.updateGeminiStatus('disconnected');
            });
        }

        // Verify API button
        const verifyBtn = document.getElementById('verify-api-btn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => {
                this.verifyGeminiAPI();
            });
        }

        // Close modal button
        const closeModalBtn = document.getElementById('close-modal-btn');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                this.closeAIAnalysisModal();
            });
        }

        // Close modal on backdrop click
        const modal = document.getElementById('ai-analysis-modal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeAIAnalysisModal();
                }
            });
        }
    }

    setupAIAnalysisListeners() {
        console.log('[AI] Setting up AI Analysis listeners...');

        // Use event delegation for dynamically added buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.ai-analysis-btn')) {
                const button = e.target.closest('.ai-analysis-btn');
                const symbol = button.dataset.symbol;
                if (symbol) {
                    this.startAIAnalysis(symbol);
                }
            }
        });
    }

    initializeGeminiUI() {
        console.log('[GEMINI] Initializing Gemini UI...');

        // Set initial API key if exists
        const apiKeyInput = document.getElementById('gemini-api-key');
        if (apiKeyInput && this.geminiApiKey) {
            apiKeyInput.value = this.geminiApiKey;
            // Auto-verify if API key exists
            setTimeout(() => {
                this.verifyGeminiAPI();
            }, 1000);
        } else {
            this.updateGeminiStatus('disconnected');
        }
    }

    async verifyGeminiAPI() {
        console.log('[GEMINI] Verifying Gemini API...');

        if (!this.geminiApiKey || this.geminiApiKey.trim() === '') {
            this.showNotification('Please enter your Gemini API Key', 'warning');
            return;
        }

        this.updateGeminiStatus('testing');

        try {
            // Test API with Gemini 2.0 Flash
            const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-goog-api-key': this.geminiApiKey
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: "Test connection. Respond with 'OK' only."
                        }]
                    }]
                })
            });

            if (response.ok) {
                const data = await response.json();
                console.log('[GEMINI] API Response:', data);

                if (data.candidates && data.candidates.length > 0) {
                    this.isGeminiConnected = true;
                    this.updateGeminiStatus('connected');
                    this.showNotification('Gemini 2.0 Flash API connected successfully! 🤖✨', 'success');
                    console.log('[GEMINI] API verification successful with Gemini 2.0 Flash');
                } else {
                    throw new Error('Invalid API response - no candidates returned');
                }
            } else {
                const errorData = await response.text();
                console.error('[GEMINI] API Error Response:', errorData);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorData}`);
            }

        } catch (error) {
            console.error('[GEMINI] API verification failed:', error);
            this.isGeminiConnected = false;
            this.updateGeminiStatus('disconnected');
            this.showNotification(`Gemini API verification failed: ${error.message}`, 'error');
        }
    }

    updateGeminiStatus(status) {
        const statusIndicator = document.getElementById('api-status-indicator');
        const verifyBtn = document.getElementById('verify-api-btn');

        if (!statusIndicator || !verifyBtn) return;

        // Reset classes
        statusIndicator.className = 'api-status-indicator';
        verifyBtn.className = 'verify-api-btn';

        switch (status) {
            case 'connected':
                statusIndicator.classList.add('connected');
                statusIndicator.innerHTML = '<span class="status-dot">🟢</span><span class="status-text">Connected</span>';
                verifyBtn.classList.add('success');
                verifyBtn.innerHTML = '<span class="btn-icon">✅</span><span class="btn-text">Connected</span>';
                verifyBtn.disabled = false;
                break;

            case 'disconnected':
                statusIndicator.classList.add('disconnected');
                statusIndicator.innerHTML = '<span class="status-dot">🔴</span><span class="status-text">Disconnected</span>';
                verifyBtn.innerHTML = '<span class="btn-icon">🔍</span><span class="btn-text">Verifikasi API</span>';
                verifyBtn.disabled = false;
                break;

            case 'testing':
                statusIndicator.classList.add('testing');
                statusIndicator.innerHTML = '<span class="status-dot">🟡</span><span class="status-text">Testing...</span>';
                verifyBtn.classList.add('testing');
                verifyBtn.innerHTML = '<span class="loading-spinner"></span><span class="btn-text">Testing...</span>';
                verifyBtn.disabled = true;
                break;
        }
    }

    // AI Analysis Functions
    async startAIAnalysis(symbol) {
        console.log(`[AI] Starting AI analysis for ${symbol}...`);

        if (!this.isGeminiConnected) {
            this.showNotification('Please connect to Gemini API first in Settings', 'warning');
            return;
        }

        // Show modal
        this.showAIAnalysisModal(symbol);

        // Show chart for the symbol
        await this.showChartForSymbol(symbol);

        try {
            // Get comprehensive data for the symbol
            const marketData = await this.getMarketData(symbol);
            const technicalData = await this.getTechnicalIndicators(symbol);

            // Create comprehensive prompt
            const prompt = this.createAIPrompt(symbol, marketData, technicalData);

            // Send to Gemini AI
            const analysis = await this.sendToGeminiAI(prompt);

            // Display result
            this.displayAIAnalysisResult(analysis);

            // Add AI overlay to chart
            this.addAIOverlayToChart(analysis);

            // Add Gemini decision lines
            this.addGeminiDecisionLines(analysis);

            // Show trading simulator for the symbol
            this.showTradingSimulatorInAnalysis(symbol);

        } catch (error) {
            console.error('[AI] Analysis failed:', error);
            this.displayAIAnalysisError(error.message);
        }
    }

    showAIAnalysisModal(symbol) {
        const modal = document.getElementById('ai-analysis-modal');
        const title = document.getElementById('ai-analysis-title');
        const loading = document.getElementById('ai-analysis-loading');
        const result = document.getElementById('ai-analysis-result');

        if (modal && title && loading && result) {
            title.textContent = `🤖 Analisis AI - ${symbol}`;
            loading.style.display = 'block';
            result.style.display = 'none';
            modal.classList.add('show');

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }

    closeAIAnalysisModal() {
        const modal = document.getElementById('ai-analysis-modal');
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    async getMarketData(symbol) {
        console.log(`[AI] Fetching market data for ${symbol}...`);

        try {
            // Try to get data from our API first
            const response = await fetch(`/api/market-data/${symbol}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.data;
                }
            }

            // Fallback to mock data for demo
            return this.getMockMarketData(symbol);

        } catch (error) {
            console.warn('[AI] Failed to fetch real market data, using mock data');
            return this.getMockMarketData(symbol);
        }
    }

    getMockMarketData(symbol) {
        // Mock market data for demonstration
        const basePrice = symbol.includes('BTC') ? 43250 : 2650;
        const variation = 0.02; // 2% variation

        return {
            symbol: symbol,
            price: basePrice * (1 + (Math.random() - 0.5) * variation),
            open: basePrice * (1 + (Math.random() - 0.5) * variation),
            high: basePrice * (1 + Math.random() * variation),
            low: basePrice * (1 - Math.random() * variation),
            volume: Math.floor(Math.random() * 1000000) + 500000,
            change24h: (Math.random() - 0.5) * 10,
            changePercent24h: (Math.random() - 0.5) * 5,
            timestamp: new Date().toISOString()
        };
    }

    async getTechnicalIndicators(symbol) {
        console.log(`[AI] Fetching technical indicators for ${symbol}...`);

        try {
            // Try to get data from our API first
            const response = await fetch(`/api/technical-indicators/${symbol}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.data;
                }
            }

            // Fallback to mock data for demo
            return this.getMockTechnicalData(symbol);

        } catch (error) {
            console.warn('[AI] Failed to fetch real technical data, using mock data');
            return this.getMockTechnicalData(symbol);
        }
    }

    getMockTechnicalData(symbol) {
        // Mock technical indicators for demonstration
        return {
            rsi: Math.floor(Math.random() * 100),
            macd: {
                macd: (Math.random() - 0.5) * 100,
                signal: (Math.random() - 0.5) * 100,
                histogram: (Math.random() - 0.5) * 50
            },
            bollinger: {
                upper: 45000,
                middle: 43250,
                lower: 41500
            },
            ema: {
                ema20: 43100,
                ema50: 42800,
                ema200: 41500
            },
            sma: {
                sma20: 43150,
                sma50: 42900,
                sma200: 41600
            },
            stochastic: {
                k: Math.floor(Math.random() * 100),
                d: Math.floor(Math.random() * 100)
            },
            williams: Math.floor(Math.random() * 100) - 100,
            atr: Math.floor(Math.random() * 1000) + 500,
            adx: Math.floor(Math.random() * 100),
            cci: (Math.random() - 0.5) * 400,
            momentum: (Math.random() - 0.5) * 1000,
            roc: (Math.random() - 0.5) * 10,
            support_levels: [41000, 42000, 42500],
            resistance_levels: [44000, 45000, 46000],
            trend: Math.random() > 0.5 ? 'bullish' : 'bearish',
            signal: ['BUY', 'SELL', 'HOLD'][Math.floor(Math.random() * 3)]
        };
    }

    createAIPrompt(symbol, marketData, technicalData) {
        console.log(`[AI] Creating comprehensive prompt for ${symbol}...`);

        const prompt = `
[PEMBUKAAN]
Bertindaklah sebagai trader profesional dalam grup sinyal Telegram. Berikan analisis yang informatif dan interaktif untuk pair ${symbol}.

[DATA TEKNIS REAL-TIME]
Nama Pair: ${symbol}
Harga Saat Ini: $${marketData.price.toFixed(6)}
Open: $${marketData.open.toFixed(6)}
High: $${marketData.high.toFixed(6)}
Low: $${marketData.low.toFixed(6)}
Volume 24h: ${marketData.volume.toLocaleString()}
Perubahan 24h: ${marketData.changePercent24h.toFixed(2)}%

[INDIKATOR TEKNIS LENGKAP]
RSI (14): ${technicalData.rsi}
MACD: ${technicalData.macd.macd.toFixed(4)} | Signal: ${technicalData.macd.signal.toFixed(4)} | Histogram: ${technicalData.macd.histogram.toFixed(4)}
Bollinger Bands: Upper: $${technicalData.bollinger.upper} | Middle: $${technicalData.bollinger.middle} | Lower: $${technicalData.bollinger.lower}
EMA: 20: $${technicalData.ema.ema20} | 50: $${technicalData.ema.ema50} | 200: $${technicalData.ema.ema200}
SMA: 20: $${technicalData.sma.sma20} | 50: $${technicalData.sma.sma50} | 200: $${technicalData.sma.sma200}
Stochastic: %K: ${technicalData.stochastic.k} | %D: ${technicalData.stochastic.d}
Williams %R: ${technicalData.williams}
ATR: ${technicalData.atr}
ADX: ${technicalData.adx}
CCI: ${technicalData.cci.toFixed(2)}
Momentum: ${technicalData.momentum.toFixed(2)}
ROC: ${technicalData.roc.toFixed(2)}%

[LEVEL SUPPORT & RESISTANCE]
Support Levels: ${technicalData.support_levels.map(level => '$' + level).join(', ')}
Resistance Levels: ${technicalData.resistance_levels.map(level => '$' + level).join(', ')}

[ANALISIS SISTEM INTERNAL]
Trend Terdeteksi: ${technicalData.trend.toUpperCase()}
Keputusan Sistem: ${technicalData.signal}

[INSTRUKSI ANALISIS]
1. Lakukan riset tambahan menggunakan informasi terkini dari internet tentang ${symbol}
2. Analisis semua indikator teknis di atas secara komprehensif
3. Berikan keputusan trading yang tegas: BUY/SELL/HOLD dengan alasan yang jelas dan detail
4. Tentukan entry point yang optimal berdasarkan analisis teknis
5. Berikan tingkat keyakinan dalam persentase (0-100%)
6. Hitung stop loss, take profit, dan leverage yang disarankan untuk modal 3 juta rupiah
7. Berikan prediksi pergerakan harga dalam 24 jam ke depan
8. Sertakan analisis risiko dan manajemen modal
9. Berikan rekomendasi timeframe trading yang optimal
10. Akhiri dengan: "Signal Analyzer AI by BOBACHEESE"

Berikan analisis yang detail, profesional, dan actionable untuk trader.
        `.trim();

        return prompt;
    }

    async sendToGeminiAI(prompt) {
        console.log('[AI] Sending prompt to Gemini AI...');

        try {
            const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-goog-api-key': this.geminiApiKey
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 2048,
                    }
                })
            });

            if (!response.ok) {
                const errorData = await response.text();
                console.error('[AI] Gemini API Error:', errorData);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorData}`);
            }

            const data = await response.json();
            console.log('[AI] Gemini 2.0 Flash Response:', data);

            if (data.candidates && data.candidates.length > 0) {
                const analysis = data.candidates[0].content.parts[0].text;
                console.log('[AI] Received analysis from Gemini 2.0 Flash');
                return analysis;
            } else {
                console.error('[AI] No candidates in response:', data);
                throw new Error('No analysis received from Gemini 2.0 Flash');
            }

        } catch (error) {
            console.error('[AI] Failed to get analysis from Gemini AI:', error);
            throw error;
        }
    }

    displayAIAnalysisResult(analysis) {
        const loading = document.getElementById('ai-analysis-loading');
        const result = document.getElementById('ai-analysis-result');

        if (loading && result) {
            loading.style.display = 'none';
            result.style.display = 'block';

            // Format the analysis text
            const formattedAnalysis = this.formatAnalysisText(analysis);
            result.innerHTML = formattedAnalysis;

            this.showNotification('AI Analysis completed! 🤖', 'success');
        }
    }

    displayAIAnalysisError(errorMessage) {
        const loading = document.getElementById('ai-analysis-loading');
        const result = document.getElementById('ai-analysis-result');

        if (loading && result) {
            loading.style.display = 'none';
            result.style.display = 'block';
            result.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-muted);">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">❌</div>
                    <h3>Analysis Failed</h3>
                    <p>Error: ${errorMessage}</p>
                    <p style="font-size: 0.9rem; margin-top: 1rem;">
                        Please check your Gemini API key and try again.
                    </p>
                </div>
            `;

            this.showNotification(`AI Analysis failed: ${errorMessage}`, 'error');
        }
    }

    formatAnalysisText(text) {
        // Format the analysis text with proper HTML structure
        let formatted = text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold text
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic text
            .replace(/\n\n/g, '</p><p>') // Paragraphs
            .replace(/\n/g, '<br>') // Line breaks
            .replace(/(\d+\.)/g, '<br><strong>$1</strong>') // Numbered lists
            .replace(/(BUY|SELL|HOLD)/g, '<span style="background: var(--pastel-blue); padding: 2px 8px; border-radius: 4px; font-weight: bold;">$1</span>') // Highlight signals
            .replace(/(\d+%)/g, '<span style="color: var(--text-primary); font-weight: bold;">$1</span>') // Highlight percentages
            .replace(/(Signal Analyzer AI by BOBACHEESE)/g, '<div style="margin-top: 2rem; padding: 1rem; background: var(--gradient-primary); color: white; border-radius: 8px; text-align: center; font-weight: bold;">$1</div>');

        return `<p>${formatted}</p>`;
    }

    // Chart Functions
    setupChartEventListeners() {
        console.log('[CHART] Setting up chart event listeners...');

        // Timeframe selector
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('timeframe-btn')) {
                const timeframe = e.target.dataset.timeframe;
                this.changeTimeframe(timeframe);
            }
        });

        // AI overlay toggle
        const aiOverlayToggle = document.getElementById('ai-overlay-toggle');
        if (aiOverlayToggle) {
            aiOverlayToggle.addEventListener('click', () => {
                this.toggleAIOverlay();
            });
        }

        // Fullscreen toggle
        const fullscreenBtn = document.getElementById('chart-fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleChartFullscreen();
            });
        }

        // AI overlay controls
        const overlayControls = ['show-entry-points', 'show-stop-loss', 'show-take-profit', 'show-prediction'];
        overlayControls.forEach(controlId => {
            const control = document.getElementById(controlId);
            if (control) {
                control.addEventListener('change', () => {
                    this.updateAIOverlay();
                });
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            switch (e.key) {
                case 'f':
                case 'F':
                    if (this.currentChart) {
                        this.toggleChartFullscreen();
                    }
                    break;
                case '1':
                    this.changeTimeframe('1m');
                    break;
                case '5':
                    this.changeTimeframe('5m');
                    break;
                case 'h':
                case 'H':
                    this.changeTimeframe('1h');
                    break;
                case 'd':
                case 'D':
                    this.changeTimeframe('1d');
                    break;
            }
        });
    }

    async showChartForSymbol(symbol) {
        console.log(`[CHART] Showing chart for ${symbol}...`);

        this.currentSymbol = symbol;

        // Update chart title
        const chartSymbol = document.getElementById('chart-symbol');
        if (chartSymbol) {
            chartSymbol.textContent = `${symbol} Chart Analysis`;
        }

        // Show chart container
        const chartContainer = document.getElementById('chart-container');
        if (chartContainer) {
            chartContainer.style.display = 'block';
        }

        // Load chart data
        await this.loadChartData(symbol, this.currentTimeframe);
    }

    async loadChartData(symbol, timeframe) {
        console.log(`[CHART] Loading chart data for ${symbol} ${timeframe}...`);

        // Show loading state
        this.showChartLoading(true);

        try {
            // Fetch chart data from API
            const response = await fetch(`/api/chart-data/${symbol}?interval=${timeframe}&limit=100`);
            const data = await response.json();

            if (data.success) {
                console.log(`[CHART] Received ${data.count} candles for ${symbol}`);
                this.renderChart(data.data, symbol, timeframe);
            } else {
                throw new Error(data.error || 'Failed to fetch chart data');
            }

        } catch (error) {
            console.error('[CHART] Error loading chart data:', error);
            this.showChartError(error.message);
        } finally {
            this.showChartLoading(false);
        }
    }

    renderChart(chartData, symbol, timeframe) {
        console.log(`[CHART] Rendering chart for ${symbol}...`);

        const canvas = document.getElementById('trading-chart');
        if (!canvas) {
            console.error('[CHART] Chart canvas not found');
            return;
        }

        // Destroy existing chart
        if (this.currentChart) {
            this.currentChart.destroy();
        }

        // Prepare data for Chart.js with proper timestamp handling
        const candlestickData = chartData.map(candle => {
            // Ensure timestamp is properly converted
            let timestamp = candle.timestamp;
            if (typeof timestamp === 'string') {
                timestamp = new Date(timestamp).getTime();
            } else if (typeof timestamp === 'number' && timestamp > 1e12) {
                // Already in milliseconds
                timestamp = timestamp;
            } else if (typeof timestamp === 'number') {
                // Convert seconds to milliseconds
                timestamp = timestamp * 1000;
            }

            return {
                x: timestamp,
                o: parseFloat(candle.open) || 0,
                h: parseFloat(candle.high) || 0,
                l: parseFloat(candle.low) || 0,
                c: parseFloat(candle.close) || 0
            };
        });

        const volumeData = chartData.map(candle => {
            // Ensure timestamp is properly converted
            let timestamp = candle.timestamp;
            if (typeof timestamp === 'string') {
                timestamp = new Date(timestamp).getTime();
            } else if (typeof timestamp === 'number' && timestamp > 1e12) {
                timestamp = timestamp;
            } else if (typeof timestamp === 'number') {
                timestamp = timestamp * 1000;
            }

            return {
                x: timestamp,
                y: parseFloat(candle.volume) || 0
            };
        });

        // Professional Chart Configuration (using line chart for compatibility)
        const config = {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: `${symbol} Price`,
                        data: candlestickData.map(candle => ({
                            x: candle.x,
                            y: candle.c  // Use close price for line chart
                        })),
                        borderColor: '#00C087',
                        backgroundColor: 'rgba(0, 192, 135, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    },
                    {
                        label: 'Volume',
                        data: volumeData,
                        type: 'bar',
                        backgroundColor: function(context) {
                            const index = context.dataIndex;
                            const current = chartData[index];
                            const previous = chartData[index - 1];
                            if (!previous) return 'rgba(153, 153, 153, 0.3)';
                            return current.close >= previous.close ?
                                'rgba(0, 192, 135, 0.3)' : 'rgba(248, 73, 96, 0.3)';
                        },
                        borderColor: function(context) {
                            const index = context.dataIndex;
                            const current = chartData[index];
                            const previous = chartData[index - 1];
                            if (!previous) return 'rgba(153, 153, 153, 0.8)';
                            return current.close >= previous.close ?
                                'rgba(0, 192, 135, 0.8)' : 'rgba(248, 73, 96, 0.8)';
                        },
                        borderWidth: 1,
                        yAxisID: 'volume',
                        barThickness: 'flex',
                        maxBarThickness: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 50,
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false  // Hide default legend for cleaner look
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#333',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                return new Date(context[0].parsed.x).toLocaleString();
                            },
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    const data = context.raw;
                                    return [
                                        `Open: $${data.o.toFixed(6)}`,
                                        `High: $${data.h.toFixed(6)}`,
                                        `Low: $${data.l.toFixed(6)}`,
                                        `Close: $${data.c.toFixed(6)}`
                                    ];
                                } else {
                                    return `Volume: ${context.parsed.y.toLocaleString()}`;
                                }
                            }
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'x'
                        },
                        zoom: {
                            wheel: {
                                enabled: true,
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'x',
                        }
                    },
                    annotation: {
                        annotations: {}
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: timeframe.includes('m') ? 'minute' : timeframe.includes('h') ? 'hour' : 'day',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'MMM dd HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#999',
                            maxTicksLimit: 8
                        },
                        title: {
                            display: false
                        }
                    },
                    y: {
                        type: 'linear',
                        position: 'right',
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#999',
                            callback: function(value) {
                                return '$' + value.toFixed(6);
                            },
                            maxTicksLimit: 8
                        },
                        title: {
                            display: false
                        }
                    },
                    volume: {
                        type: 'linear',
                        position: 'right',
                        max: Math.max(...volumeData.map(d => d.y)) * 4,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            display: false
                        },
                        title: {
                            display: false
                        }
                    }
                }
            }
        };

        // Register required plugins
        if (!Chart.registry.plugins.get('annotation')) {
            Chart.register(ChartAnnotation);
        }
        if (!Chart.registry.plugins.get('zoom')) {
            Chart.register(ChartZoom);
        }

        // Create chart
        this.currentChart = new Chart(canvas, config);

        // Show canvas
        canvas.style.display = 'block';

        console.log('[CHART] Professional chart rendered successfully');
    }

    showChartLoading(show) {
        const loading = document.getElementById('chart-loading');
        const canvas = document.getElementById('trading-chart');
        const error = document.getElementById('chart-error');

        if (loading) loading.style.display = show ? 'flex' : 'none';
        if (canvas) canvas.style.display = show ? 'none' : 'block';
        if (error) error.style.display = 'none';
    }

    showChartError(message) {
        const loading = document.getElementById('chart-loading');
        const canvas = document.getElementById('trading-chart');
        const error = document.getElementById('chart-error');

        if (loading) loading.style.display = 'none';
        if (canvas) canvas.style.display = 'none';
        if (error) {
            error.style.display = 'block';
            const errorMsg = error.querySelector('p');
            if (errorMsg) errorMsg.textContent = message;
        }
    }

    changeTimeframe(timeframe) {
        console.log(`[CHART] Changing timeframe to ${timeframe}...`);

        // Update active button
        document.querySelectorAll('.timeframe-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.querySelector(`[data-timeframe="${timeframe}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        this.currentTimeframe = timeframe;

        // Reload chart data if symbol is selected
        if (this.currentSymbol) {
            this.loadChartData(this.currentSymbol, timeframe);
        }
    }

    toggleAIOverlay() {
        const overlayControls = document.getElementById('ai-overlay-controls');
        if (overlayControls) {
            const isVisible = overlayControls.style.display !== 'none';
            overlayControls.style.display = isVisible ? 'none' : 'block';

            const toggleBtn = document.getElementById('ai-overlay-toggle');
            if (toggleBtn) {
                toggleBtn.style.opacity = isVisible ? '0.6' : '1';
            }
        }
    }

    toggleChartFullscreen() {
        const chartContainer = document.getElementById('chart-container');
        if (!chartContainer) return;

        this.isChartFullscreen = !this.isChartFullscreen;

        if (this.isChartFullscreen) {
            chartContainer.classList.add('chart-fullscreen');
            document.body.style.overflow = 'hidden';
        } else {
            chartContainer.classList.remove('chart-fullscreen');
            document.body.style.overflow = '';
        }

        // Resize chart
        if (this.currentChart) {
            setTimeout(() => {
                this.currentChart.resize();
            }, 300);
        }
    }

    addAIOverlayToChart(aiAnalysisData) {
        console.log('[CHART] Adding AI overlay to chart...');

        if (!this.currentChart || !aiAnalysisData) {
            console.warn('[CHART] No chart or AI data available for overlay');
            return;
        }

        // Parse AI analysis for trading levels
        const aiLevels = this.parseAIAnalysisForLevels(aiAnalysisData);

        if (!aiLevels) {
            console.warn('[CHART] Could not parse AI levels from analysis');
            return;
        }

        this.aiOverlayData = aiLevels;

        // Add annotation lines to chart
        this.updateAIOverlay();

        // Show overlay controls
        const overlayControls = document.getElementById('ai-overlay-controls');
        if (overlayControls) {
            overlayControls.style.display = 'block';
        }

        console.log('[CHART] AI overlay added successfully');
    }

    parseAIAnalysisForLevels(analysisText) {
        console.log('[CHART] Parsing AI analysis for trading levels...');

        try {
            // Extract price levels from AI analysis text
            const priceRegex = /\$?(\d+\.?\d*)/g;
            const prices = [];
            let match;

            while ((match = priceRegex.exec(analysisText)) !== null) {
                const price = parseFloat(match[1]);
                if (price > 0 && price < 1000000) { // Reasonable price range
                    prices.push(price);
                }
            }

            if (prices.length < 3) {
                // Generate mock levels based on current price for demo
                const currentPrice = this.getCurrentChartPrice();
                return {
                    entry: currentPrice * (1 + (Math.random() - 0.5) * 0.02),
                    stopLoss: currentPrice * (1 - Math.random() * 0.05),
                    takeProfit: currentPrice * (1 + Math.random() * 0.08),
                    prediction: currentPrice * (1 + (Math.random() - 0.5) * 0.1),
                    confidence: Math.floor(Math.random() * 30) + 70 // 70-100%
                };
            }

            // Use extracted prices
            prices.sort((a, b) => a - b);

            return {
                entry: prices[Math.floor(prices.length / 2)], // Middle price as entry
                stopLoss: prices[0], // Lowest as stop loss
                takeProfit: prices[prices.length - 1], // Highest as take profit
                prediction: prices[Math.floor(prices.length / 2)] * 1.05, // 5% above entry
                confidence: Math.floor(Math.random() * 30) + 70
            };

        } catch (error) {
            console.error('[CHART] Error parsing AI analysis:', error);
            return null;
        }
    }

    getCurrentChartPrice() {
        if (!this.currentChart || !this.currentChart.data.datasets[0].data.length) {
            return 50000; // Default fallback
        }

        const lastDataPoint = this.currentChart.data.datasets[0].data;
        return lastDataPoint[lastDataPoint.length - 1].y;
    }

    updateAIOverlay() {
        if (!this.currentChart || !this.aiOverlayData) return;

        console.log('[CHART] Updating AI overlay...');

        // Get overlay settings
        const showEntry = document.getElementById('show-entry-points')?.checked ?? true;
        const showStopLoss = document.getElementById('show-stop-loss')?.checked ?? true;
        const showTakeProfit = document.getElementById('show-take-profit')?.checked ?? true;
        const showPrediction = document.getElementById('show-prediction')?.checked ?? true;

        // Remove existing annotations
        if (this.currentChart.options.plugins.annotation) {
            this.currentChart.options.plugins.annotation.annotations = {};
        } else {
            this.currentChart.options.plugins.annotation = { annotations: {} };
        }

        const annotations = {};

        // Add AI level lines
        if (showEntry) {
            annotations.entryLine = {
                type: 'line',
                yMin: this.aiOverlayData.entry,
                yMax: this.aiOverlayData.entry,
                borderColor: '#00ff00',
                borderWidth: 2,
                label: {
                    content: 'AI ENTRY',
                    enabled: true,
                    position: 'end'
                }
            };
        }

        if (showStopLoss) {
            annotations.stopLossLine = {
                type: 'line',
                yMin: this.aiOverlayData.stopLoss,
                yMax: this.aiOverlayData.stopLoss,
                borderColor: '#ff0000',
                borderWidth: 2,
                label: {
                    content: 'AI STOP LOSS',
                    enabled: true,
                    position: 'end'
                }
            };
        }

        if (showTakeProfit) {
            annotations.takeProfitLine = {
                type: 'line',
                yMin: this.aiOverlayData.takeProfit,
                yMax: this.aiOverlayData.takeProfit,
                borderColor: '#0066ff',
                borderWidth: 2,
                label: {
                    content: 'AI TAKE PROFIT',
                    enabled: true,
                    position: 'end'
                }
            };
        }

        if (showPrediction) {
            annotations.predictionLine = {
                type: 'line',
                yMin: this.aiOverlayData.prediction,
                yMax: this.aiOverlayData.prediction,
                borderColor: '#ffaa00',
                borderWidth: 2,
                borderDash: [5, 5],
                label: {
                    content: 'AI PREDICTION',
                    enabled: true,
                    position: 'end'
                }
            };
        }

        this.currentChart.options.plugins.annotation.annotations = annotations;
        this.currentChart.update();

        console.log('[CHART] AI overlay updated');
    }

    // Configuration Functions
    setupConfigurationListeners() {
        console.log('[CONFIG] Setting up configuration listeners...');

        // Signal limit slider
        const signalLimitSlider = document.getElementById('signal-limit-slider');
        const signalLimitValue = document.getElementById('signal-limit-value');

        if (signalLimitSlider && signalLimitValue) {
            // Set initial value
            signalLimitSlider.value = this.signalDisplayLimit;
            signalLimitValue.textContent = this.signalDisplayLimit;

            // Add event listener with validation
            signalLimitSlider.addEventListener('input', (e) => {
                const value = Math.min(Math.max(parseInt(e.target.value), 5), 50);
                this.updateSignalLimit(value);
            });
        }
    }

    updateSignalLimit(newLimit) {
        console.log(`[CONFIG] Updating signal limit to ${newLimit}...`);

        this.signalDisplayLimit = newLimit;
        localStorage.setItem('signal_display_limit', newLimit);

        // Update UI
        const signalLimitValue = document.getElementById('signal-limit-value');
        if (signalLimitValue) {
            signalLimitValue.textContent = newLimit;
        }

        // Reload signals with new limit
        this.loadSignals();
    }

    updateSignalCounter(shown, total) {
        const counter = document.getElementById('signal-counter');
        if (counter) {
            counter.classList.add('updating');
            counter.textContent = `Showing ${shown} of ${total} total opportunities`;

            setTimeout(() => {
                counter.classList.remove('updating');
            }, 300);
        }
    }

    showSignalsLoading(show) {
        const container = document.getElementById('signals-container');
        if (!container) return;

        if (show) {
            if (!container.querySelector('.loading-overlay')) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.id = 'signals-loading';
                overlay.innerHTML = `
                    <div class="loading-spinner"></div>
                    <p>Loading signals...</p>
                `;
                container.style.position = 'relative';
                container.appendChild(overlay);
            }
        } else {
            const overlay = container.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    }

    // Trading Simulator Functions
    setupTradingSimulatorListeners() {
        console.log('[SIMULATOR] Setting up trading simulator listeners...');

        // Trading simulator button event delegation (for legacy buttons if any)
        document.addEventListener('click', (e) => {
            if (e.target.closest('.trading-sim-btn')) {
                const button = e.target.closest('.trading-sim-btn');
                const symbol = button.dataset.symbol;
                if (symbol) {
                    this.openTradingSimulator(symbol);
                }
            }
        });

        // Simulator toggle button
        const toggleBtn = document.getElementById('simulator-toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleTradingSimulator();
            });
        }

        // Enhanced leverage slider with validation
        const leverageSlider = document.getElementById('leverage');
        const leverageValue = document.getElementById('leverage-value');
        if (leverageSlider && leverageValue) {
            leverageSlider.addEventListener('input', (e) => {
                leverageValue.textContent = e.target.value + 'x';
                this.validateLeverage(e.target.value);
                this.updatePositionCalculation();
            });
        }

        // Position buttons with validation
        const openPositionBtn = document.getElementById('open-position');
        const closePositionBtn = document.getElementById('close-position');

        if (openPositionBtn) {
            openPositionBtn.addEventListener('click', () => {
                if (this.validateAllInputs()) {
                    this.openPosition();
                }
            });
        }

        if (closePositionBtn) {
            closePositionBtn.addEventListener('click', () => {
                this.closePosition();
            });
        }

        // Enhanced real-time calculation updates with validation
        const quantityInput = document.getElementById('quantity');
        if (quantityInput) {
            quantityInput.addEventListener('input', (e) => {
                this.validateQuantity(e.target.value);
                this.updatePositionCalculation();
            });

            quantityInput.addEventListener('blur', (e) => {
                this.validateQuantity(e.target.value, true);
            });
        }

        const orderTypeSelect = document.getElementById('order-type');
        if (orderTypeSelect) {
            orderTypeSelect.addEventListener('change', () => {
                this.updatePositionCalculation();
            });
        }

        // Position type radio buttons
        document.querySelectorAll('input[name="position"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.updatePositionCalculation();
            });
        });
    }

    showTradingSimulatorInAnalysis(symbol) {
        console.log(`[SIMULATOR] Showing trading simulator for ${symbol} in analysis...`);

        this.currentSymbol = symbol;

        // Show trading simulator container
        const container = document.getElementById('trading-simulator-container');
        if (container) {
            container.style.display = 'block';
        }

        // Reset form
        this.resetSimulatorForm();

        // Load current price
        this.loadCurrentPrice(symbol);

        // Ensure simulator content is visible
        const content = document.getElementById('simulator-content');
        if (content) {
            content.classList.remove('collapsed');
        }
    }

    toggleTradingSimulator() {
        const content = document.getElementById('simulator-content');
        const toggleBtn = document.getElementById('simulator-toggle-btn');

        if (content && toggleBtn) {
            const isCollapsed = content.classList.contains('collapsed');

            if (isCollapsed) {
                content.classList.remove('collapsed');
                toggleBtn.innerHTML = '<span>📈</span><span>Hide Simulator</span>';
            } else {
                content.classList.add('collapsed');
                toggleBtn.innerHTML = '<span>📊</span><span>Show Simulator</span>';
            }
        }
    }

    openTradingSimulator(symbol) {
        console.log(`[SIMULATOR] Opening trading simulator for ${symbol}...`);

        // For backward compatibility - redirect to AI analysis
        this.startAIAnalysis(symbol);
    }

    closeTradingSimulator() {
        // Hide trading simulator container
        const container = document.getElementById('trading-simulator-container');
        if (container) {
            container.style.display = 'none';
        }
    }

    resetSimulatorForm() {
        // Reset form values
        const elements = {
            'order-type': 'market',
            'quantity': '',
            'leverage': '1'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        // Reset radio buttons
        const longRadio = document.querySelector('input[name="position"][value="long"]');
        if (longRadio) {
            longRadio.checked = true;
        }

        // Reset position data
        this.isPositionOpen = false;
        this.positionData = null;

        // Update buttons
        this.updatePositionButtons();

        // Clear calculations
        this.clearPositionCalculation();
    }

    async loadCurrentPrice(symbol) {
        try {
            const response = await fetch(`/api/market-data/${symbol}`);
            const data = await response.json();

            if (data.success) {
                const price = data.data.price;
                const entryPriceElement = document.getElementById('entry-price');
                const currentPriceElement = document.getElementById('current-price');

                if (entryPriceElement) entryPriceElement.textContent = `$${price.toFixed(6)}`;
                if (currentPriceElement) currentPriceElement.textContent = `$${price.toFixed(6)}`;

                this.currentPrice = price;
                this.updatePositionCalculation();
            }
        } catch (error) {
            console.error('[SIMULATOR] Error loading current price:', error);
        }
    }

    updatePositionCalculation() {
        if (!this.currentPrice) return;

        const quantity = parseFloat(document.getElementById('quantity')?.value || 0);
        const leverage = parseInt(document.getElementById('leverage')?.value || 1);
        const position = document.querySelector('input[name="position"]:checked')?.value || 'long';

        if (quantity <= 0) {
            this.clearPositionCalculation();
            return;
        }

        // Calculate margin used
        const marginUsed = (this.currentPrice * quantity) / leverage;

        // Update margin display
        const marginElement = document.getElementById('margin-used');
        if (marginElement) {
            marginElement.textContent = `$${marginUsed.toFixed(2)}`;
        }

        // If position is open, calculate P&L
        if (this.isPositionOpen && this.positionData) {
            const pnl = this.calculatePnL(
                this.positionData.entryPrice,
                this.currentPrice,
                this.positionData.quantity,
                this.positionData.leverage,
                this.positionData.position
            );

            this.updatePnLDisplay(pnl);
        }
    }

    calculatePnL(entryPrice, currentPrice, quantity, leverage, position) {
        const priceChange = currentPrice - entryPrice;
        const direction = position === 'long' ? 1 : -1;
        const unrealizedPnL = priceChange * quantity * direction * leverage;
        const roe = (unrealizedPnL / ((entryPrice * quantity) / leverage)) * 100;

        return {
            unrealizedPnL,
            roe,
            marginUsed: (entryPrice * quantity) / leverage
        };
    }

    updatePnLDisplay(pnl) {
        const unrealizedElement = document.getElementById('unrealized-pnl');
        const roeElement = document.getElementById('roe-percentage');

        if (unrealizedElement) {
            unrealizedElement.textContent = `$${pnl.unrealizedPnL.toFixed(2)}`;
            unrealizedElement.style.color = pnl.unrealizedPnL >= 0 ? '#22c55e' : '#ef4444';
        }

        if (roeElement) {
            roeElement.textContent = `${pnl.roe.toFixed(2)}%`;
            roeElement.style.color = pnl.roe >= 0 ? '#22c55e' : '#ef4444';
        }
    }

    clearPositionCalculation() {
        const elements = ['unrealized-pnl', 'roe-percentage', 'margin-used'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = '$0.00';
                element.style.color = '';
            }
        });

        const roeElement = document.getElementById('roe-percentage');
        if (roeElement) {
            roeElement.textContent = '0.00%';
        }
    }

    openPosition() {
        const quantity = parseFloat(document.getElementById('quantity')?.value || 0);
        const leverage = parseInt(document.getElementById('leverage')?.value || 1);
        const position = document.querySelector('input[name="position"]:checked')?.value || 'long';
        const orderType = document.getElementById('order-type')?.value || 'market';

        if (quantity <= 0) {
            this.showNotification('Please enter a valid quantity', 'error');
            return;
        }

        if (!this.currentPrice) {
            this.showNotification('Price data not available', 'error');
            return;
        }

        // Confirmation dialog
        const confirmation = confirm(`Open ${position.toUpperCase()} position?\n\nSymbol: ${this.currentSymbol}\nQuantity: ${quantity}\nLeverage: ${leverage}x\nEntry Price: $${this.currentPrice.toFixed(6)}`);

        if (!confirmation) return;

        // Store position data
        this.positionData = {
            symbol: this.currentSymbol,
            entryPrice: this.currentPrice,
            quantity: quantity,
            leverage: leverage,
            position: position,
            orderType: orderType,
            openTime: new Date()
        };

        this.isPositionOpen = true;
        this.updatePositionButtons();
        this.updatePositionCalculation();

        // Start auto-refresh for real-time price updates
        this.startPriceAutoRefresh();

        this.showNotification(`${position.toUpperCase()} position opened for ${this.currentSymbol}`, 'success');
    }

    closePosition() {
        if (!this.isPositionOpen || !this.positionData) {
            this.showNotification('No open position to close', 'error');
            return;
        }

        const pnl = this.calculatePnL(
            this.positionData.entryPrice,
            this.currentPrice,
            this.positionData.quantity,
            this.positionData.leverage,
            this.positionData.position
        );

        // Confirmation dialog
        const confirmation = confirm(`Close position?\n\nP&L: $${pnl.unrealizedPnL.toFixed(2)}\nROE: ${pnl.roe.toFixed(2)}%`);

        if (!confirmation) return;

        // Close position
        this.isPositionOpen = false;
        this.positionData = null;
        this.updatePositionButtons();
        this.clearPositionCalculation();

        // Stop auto-refresh
        this.stopPriceAutoRefresh();

        const pnlMessage = pnl.unrealizedPnL >= 0 ? 'Profit' : 'Loss';
        this.showNotification(`Position closed with ${pnlMessage}: $${Math.abs(pnl.unrealizedPnL).toFixed(2)}`, pnl.unrealizedPnL >= 0 ? 'success' : 'error');
    }

    updatePositionButtons() {
        const openBtn = document.getElementById('open-position');
        const closeBtn = document.getElementById('close-position');

        if (openBtn) {
            openBtn.disabled = this.isPositionOpen;
            openBtn.textContent = this.isPositionOpen ? 'Position Open' : 'Open Position';
        }

        if (closeBtn) {
            closeBtn.disabled = !this.isPositionOpen;
        }
    }

    // Enhanced Gemini Decision Lines - FIXED IMPLEMENTATION
    addGeminiDecisionLines(analysisData) {
        console.log('[CHART] Adding Gemini decision lines...', analysisData);

        if (!this.currentChart) {
            console.warn('[CHART] No chart available for Gemini lines');
            return;
        }

        // Parse analysis for price levels or use fallback
        let geminiLevels = this.parseGeminiAnalysisForLevels(analysisData);

        if (!geminiLevels || Object.keys(geminiLevels).length === 0) {
            console.log('[CHART] Using fallback levels for demonstration');
            geminiLevels = this.generateFallbackLevels();
        }

        if (!geminiLevels) {
            console.warn('[CHART] Could not generate any Gemini levels');
            return;
        }

        console.log('[CHART] Using Gemini levels:', geminiLevels);

        // Determine confidence level for line styles
        const confidence = this.extractConfidenceFromAnalysis(analysisData);
        const lineStyle = this.getLineStyleByConfidence(confidence);

        // Clear existing Gemini annotations
        if (this.currentChart.options.plugins.annotation) {
            const annotations = this.currentChart.options.plugins.annotation.annotations;
            Object.keys(annotations).forEach(key => {
                if (key.startsWith('gemini')) {
                    delete annotations[key];
                }
            });
        }

        // Add new Gemini-specific annotations
        const newAnnotations = {};

        if (geminiLevels.entry) {
            newAnnotations.geminiEntry = {
                type: 'line',
                scaleID: 'y',
                value: geminiLevels.entry,
                borderColor: '#00ff00',
                borderWidth: lineStyle.width,
                borderDash: lineStyle.dash,
                label: {
                    content: `ENTRY: $${geminiLevels.entry.toFixed(6)}`,
                    enabled: true,
                    position: 'end',
                    backgroundColor: 'rgba(0, 255, 0, 0.9)',
                    color: '#000000',
                    font: {
                        size: 10,
                        weight: 'bold'
                    },
                    padding: 4
                }
            };
        }

        if (geminiLevels.stopLoss) {
            newAnnotations.geminiStop = {
                type: 'line',
                scaleID: 'y',
                value: geminiLevels.stopLoss,
                borderColor: '#ff0000',
                borderWidth: lineStyle.width,
                borderDash: lineStyle.dash,
                label: {
                    content: `STOP: $${geminiLevels.stopLoss.toFixed(6)}`,
                    enabled: true,
                    position: 'end',
                    backgroundColor: 'rgba(255, 0, 0, 0.9)',
                    color: '#ffffff',
                    font: {
                        size: 10,
                        weight: 'bold'
                    },
                    padding: 4
                }
            };
        }

        if (geminiLevels.takeProfit) {
            newAnnotations.geminiProfit = {
                type: 'line',
                scaleID: 'y',
                value: geminiLevels.takeProfit,
                borderColor: '#0066ff',
                borderWidth: lineStyle.width,
                borderDash: lineStyle.dash,
                label: {
                    content: `PROFIT: $${geminiLevels.takeProfit.toFixed(6)}`,
                    enabled: true,
                    position: 'end',
                    backgroundColor: 'rgba(0, 102, 255, 0.9)',
                    color: '#ffffff',
                    font: {
                        size: 10,
                        weight: 'bold'
                    },
                    padding: 4
                }
            };
        }

        if (geminiLevels.trend) {
            newAnnotations.geminiTrend = {
                type: 'line',
                scaleID: 'y',
                value: geminiLevels.trend,
                borderColor: '#ff8800',
                borderWidth: lineStyle.width,
                borderDash: [5, 5],
                label: {
                    content: `TREND: $${geminiLevels.trend.toFixed(6)}`,
                    enabled: true,
                    position: 'end',
                    backgroundColor: 'rgba(255, 136, 0, 0.9)',
                    color: '#000000',
                    font: {
                        size: 10,
                        weight: 'bold'
                    },
                    padding: 4
                }
            };
        }

        // Ensure annotation plugin is configured
        if (!this.currentChart.options.plugins.annotation) {
            this.currentChart.options.plugins.annotation = { annotations: {} };
        }

        // Add new annotations
        Object.assign(this.currentChart.options.plugins.annotation.annotations, newAnnotations);

        // Force chart update
        this.currentChart.update('active');

        // Show Gemini legend
        this.showGeminiLegend();

        // Show success notification
        this.showNotification(`✅ Gemini decision lines added (${Object.keys(newAnnotations).length} lines, Confidence: ${confidence}%)`, 'success');

        console.log('[CHART] Gemini decision lines added successfully:', Object.keys(newAnnotations));
    }

    parseGeminiAnalysisForLevels(analysisText) {
        try {
            console.log('[CHART] Parsing Gemini analysis for price levels...');

            // Enhanced parsing for Gemini-specific price levels
            const lines = analysisText.split('\n');
            const levels = {};

            // More comprehensive price pattern matching
            const pricePatterns = [
                /\$?(\d+\.?\d*)/g,                    // Basic price pattern
                /(\d+\.?\d*)\s*(?:USD|USDT)/gi,       // Price with currency
                /(?:price|harga)[\s:]*\$?(\d+\.?\d*)/gi, // Price with label
                /(\d+\.?\d*)\s*(?:dollars?|usd)/gi    // Price with dollar
            ];

            lines.forEach(line => {
                const lowerLine = line.toLowerCase();

                // Look for entry points with multiple patterns
                if (lowerLine.includes('entry') || lowerLine.includes('masuk') ||
                    lowerLine.includes('buy') || lowerLine.includes('beli')) {
                    const prices = this.extractPricesFromLine(line, pricePatterns);
                    if (prices.length > 0) {
                        levels.entry = prices[0];
                    }
                }

                // Look for stop loss with multiple patterns
                if ((lowerLine.includes('stop') && lowerLine.includes('loss')) ||
                    lowerLine.includes('sl') || lowerLine.includes('stoploss')) {
                    const prices = this.extractPricesFromLine(line, pricePatterns);
                    if (prices.length > 0) {
                        levels.stopLoss = prices[0];
                    }
                }

                // Look for take profit with multiple patterns
                if ((lowerLine.includes('take') && lowerLine.includes('profit')) ||
                    lowerLine.includes('tp') || lowerLine.includes('takeprofit') ||
                    lowerLine.includes('target')) {
                    const prices = this.extractPricesFromLine(line, pricePatterns);
                    if (prices.length > 0) {
                        levels.takeProfit = prices[0];
                    }
                }

                // Look for trend prediction
                if (lowerLine.includes('trend') || lowerLine.includes('resistance') ||
                    lowerLine.includes('support') || lowerLine.includes('level')) {
                    const prices = this.extractPricesFromLine(line, pricePatterns);
                    if (prices.length > 0) {
                        levels.trend = prices[0];
                    }
                }
            });

            // Fallback logic: generate levels based on current price
            if (Object.keys(levels).length === 0) {
                console.log('[CHART] No levels found in analysis, generating fallback levels');
                const currentPrice = this.getCurrentChartPrice();
                if (currentPrice > 0) {
                    levels.entry = currentPrice;
                    levels.stopLoss = currentPrice * 0.95;  // 5% below
                    levels.takeProfit = currentPrice * 1.08; // 8% above
                    levels.trend = currentPrice * 1.05;     // 5% above
                }
            }

            // Validate and sanitize levels
            const validatedLevels = this.validateGeminiLevels(levels);

            console.log('[CHART] Parsed Gemini levels:', validatedLevels);
            return validatedLevels;

        } catch (error) {
            console.error('[CHART] Error parsing Gemini analysis:', error);
            return this.generateFallbackLevels();
        }
    }

    extractPricesFromLine(line, patterns) {
        const prices = [];
        patterns.forEach(pattern => {
            const matches = line.matchAll(pattern);
            for (const match of matches) {
                const price = parseFloat(match[1]);
                if (!isNaN(price) && price > 0) {
                    prices.push(price);
                }
            }
        });
        return prices;
    }

    validateGeminiLevels(levels) {
        const validated = {};
        const currentPrice = this.getCurrentChartPrice();

        // Validate each level
        Object.keys(levels).forEach(key => {
            const value = levels[key];
            if (typeof value === 'number' && !isNaN(value) && value > 0) {
                // Ensure levels are reasonable relative to current price
                if (currentPrice > 0) {
                    const ratio = value / currentPrice;
                    if (ratio > 0.5 && ratio < 2.0) { // Within 50% to 200% of current price
                        validated[key] = value;
                    }
                } else {
                    validated[key] = value;
                }
            }
        });

        return validated;
    }

    generateFallbackLevels() {
        const currentPrice = this.getCurrentChartPrice();
        if (currentPrice <= 0) return null;

        return {
            entry: currentPrice,
            stopLoss: currentPrice * 0.95,
            takeProfit: currentPrice * 1.08,
            trend: currentPrice * 1.05
        };
    }

    extractConfidenceFromAnalysis(analysisText) {
        try {
            // Look for confidence percentage in analysis
            const confidenceMatch = analysisText.match(/confidence[:\s]*(\d+)%/i) ||
                                  analysisText.match(/(\d+)%\s*confidence/i) ||
                                  analysisText.match(/akurasi[:\s]*(\d+)%/i);

            if (confidenceMatch) {
                return parseInt(confidenceMatch[1]);
            }

            // Default confidence based on analysis quality
            const analysisLength = analysisText.length;
            if (analysisLength > 500) return 85;
            if (analysisLength > 200) return 75;
            return 65;

        } catch (error) {
            console.error('[CHART] Error extracting confidence:', error);
            return 70; // Default confidence
        }
    }

    getLineStyleByConfidence(confidence) {
        if (confidence >= 80) {
            return { width: 3, dash: [] }; // Solid line
        } else if (confidence >= 60) {
            return { width: 2, dash: [5, 5] }; // Dashed line
        } else {
            return { width: 2, dash: [2, 2] }; // Dotted line
        }
    }

    showGeminiLegend() {
        // Add Gemini legend below chart if not exists
        const chartContainer = document.getElementById('chart-container');
        if (!chartContainer) return;

        let legend = chartContainer.querySelector('.gemini-legend');
        if (!legend) {
            legend = document.createElement('div');
            legend.className = 'gemini-legend';
            legend.innerHTML = `
                <div class="gemini-legend-header">
                    <h5>🤖 Gemini AI Decision Lines</h5>
                    <div class="gemini-legend-controls">
                        <input type="checkbox" id="show-gemini-lines" checked>
                        <label for="show-gemini-lines">Show Lines</label>
                    </div>
                </div>
                <div class="gemini-legend-items">
                    <div class="gemini-legend-item">
                        <div class="gemini-legend-color entry"></div>
                        <span>Entry Point</span>
                    </div>
                    <div class="gemini-legend-item">
                        <div class="gemini-legend-color stop"></div>
                        <span>Stop Loss</span>
                    </div>
                    <div class="gemini-legend-item">
                        <div class="gemini-legend-color profit"></div>
                        <span>Take Profit</span>
                    </div>
                    <div class="gemini-legend-item">
                        <div class="gemini-legend-color trend"></div>
                        <span>Trend Line</span>
                    </div>
                </div>
                <div class="gemini-legend-info">
                    <small>Line styles: Solid (>80%), Dashed (60-80%), Dotted (<60%)</small>
                </div>
            `;
            chartContainer.appendChild(legend);

            // Add toggle functionality
            const checkbox = legend.querySelector('#show-gemini-lines');
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.toggleGeminiLines(e.target.checked);
                });
            }

            // Add fade-in animation
            setTimeout(() => {
                legend.classList.add('fade-in');
            }, 100);
        }
    }

    toggleGeminiLines(show) {
        if (!this.currentChart || !this.currentChart.options.plugins.annotation) {
            console.warn('[CHART] No chart or annotations available for toggle');
            return;
        }

        const annotations = this.currentChart.options.plugins.annotation.annotations;
        const geminiAnnotations = ['geminiEntry', 'geminiStop', 'geminiProfit', 'geminiTrend'];

        let toggledCount = 0;
        geminiAnnotations.forEach(key => {
            if (annotations[key]) {
                annotations[key].display = show;
                toggledCount++;
            }
        });

        if (toggledCount > 0) {
            this.currentChart.update('none');
            this.showNotification(
                `Gemini decision lines ${show ? 'shown' : 'hidden'}`,
                show ? 'success' : 'info'
            );
            console.log(`[CHART] Toggled ${toggledCount} Gemini lines: ${show ? 'visible' : 'hidden'}`);
        }
    }

    getCurrentChartPrice() {
        try {
            if (!this.currentChart || !this.currentChart.data.datasets[0]) {
                return 0;
            }

            const dataset = this.currentChart.data.datasets[0];
            const lastDataPoint = dataset.data[dataset.data.length - 1];

            if (typeof lastDataPoint === 'object' && lastDataPoint.y !== undefined) {
                return lastDataPoint.y;
            } else if (typeof lastDataPoint === 'number') {
                return lastDataPoint;
            }

            return 0;
        } catch (error) {
            console.error('[CHART] Error getting current chart price:', error);
            return 0;
        }
    }

    // Enhanced Form Validation Functions
    validateQuantity(value, showMessage = false) {
        const quantityInput = document.getElementById('quantity');
        const validationMessage = document.getElementById('quantity-validation');

        if (!quantityInput) return false;

        const quantity = parseFloat(value);
        let isValid = true;
        let message = '';

        if (isNaN(quantity) || quantity <= 0) {
            isValid = false;
            message = 'Please enter a valid quantity';
        } else if (quantity < 0.001) {
            isValid = false;
            message = 'Minimum quantity is 0.001';
        } else if (quantity > 1000) {
            isValid = false;
            message = 'Maximum quantity is 1000';
        } else {
            message = 'Valid quantity';
        }

        // Update input styling
        quantityInput.classList.remove('valid', 'invalid');
        quantityInput.classList.add(isValid ? 'valid' : 'invalid');

        // Show validation message if requested
        if (validationMessage && (showMessage || !isValid)) {
            validationMessage.textContent = message;
            validationMessage.classList.remove('show', 'success');
            validationMessage.classList.add('show');
            if (isValid) validationMessage.classList.add('success');
        }

        return isValid;
    }

    validateLeverage(value) {
        const leverageSlider = document.getElementById('leverage');
        const validationMessage = document.getElementById('leverage-validation');

        if (!leverageSlider) return false;

        const leverage = parseInt(value);
        let isValid = true;
        let message = '';

        if (isNaN(leverage) || leverage < 1 || leverage > 20) {
            isValid = false;
            message = 'Leverage must be between 1x and 20x';
        } else {
            message = `${leverage}x leverage selected`;
        }

        // Update input styling
        leverageSlider.classList.remove('valid', 'invalid');
        leverageSlider.classList.add(isValid ? 'valid' : 'invalid');

        // Show validation message
        if (validationMessage) {
            validationMessage.textContent = message;
            validationMessage.classList.remove('show', 'success');
            validationMessage.classList.add('show');
            if (isValid) validationMessage.classList.add('success');
        }

        return isValid;
    }

    validateAllInputs() {
        const quantityInput = document.getElementById('quantity');
        const leverageSlider = document.getElementById('leverage');

        const quantityValid = this.validateQuantity(quantityInput?.value || '', true);
        const leverageValid = this.validateLeverage(leverageSlider?.value || '1');

        if (!quantityValid || !leverageValid) {
            this.showNotification('Please fix validation errors before opening position', 'error');
            return false;
        }

        return true;
    }

    // Enhanced Price Loading with Loading State
    async loadCurrentPrice(symbol) {
        const currentPriceElement = document.getElementById('current-price');
        const quantityInput = document.getElementById('quantity');

        if (currentPriceElement) {
            currentPriceElement.textContent = 'Loading...';
        }

        if (quantityInput) {
            quantityInput.classList.add('loading');
        }

        try {
            const response = await fetch(`/api/market-data/${symbol}`);
            const data = await response.json();

            if (data.success && data.data.price) {
                this.currentPrice = data.data.price;
                if (currentPriceElement) {
                    currentPriceElement.textContent = `$${this.currentPrice.toFixed(6)}`;
                }

                // Update position calculation
                this.updatePositionCalculation();

                console.log(`[SIMULATOR] Current price loaded: $${this.currentPrice}`);
            } else {
                throw new Error('Invalid price data received');
            }
        } catch (error) {
            console.error('[SIMULATOR] Error loading current price:', error);
            if (currentPriceElement) {
                currentPriceElement.textContent = 'Error loading price';
            }
            this.showNotification('Failed to load current price', 'error');
        } finally {
            if (quantityInput) {
                quantityInput.classList.remove('loading');
            }
        }
    }

    // Auto-refresh price every 5 seconds when position is open
    startPriceAutoRefresh() {
        if (this.priceRefreshInterval) {
            clearInterval(this.priceRefreshInterval);
        }

        this.priceRefreshInterval = setInterval(() => {
            if (this.isPositionOpen && this.currentSymbol) {
                this.loadCurrentPrice(this.currentSymbol);
            }
        }, 5000); // 5 seconds
    }

    stopPriceAutoRefresh() {
        if (this.priceRefreshInterval) {
            clearInterval(this.priceRefreshInterval);
            this.priceRefreshInterval = null;
        }
    }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('[NEW APP] DOM loaded, initializing application...');

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .notification {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 16px;
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
            border-left: 4px solid #a7c7e7;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left-color: #b8e6b8;
        }

        .notification.error {
            border-left-color: #f4c2c2;
        }

        .notification.warning {
            border-left-color: #fff3cd;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .notification-icon {
            font-size: 1.2rem;
        }

        .notification-message {
            font-weight: 500;
            color: #2d3748;
        }
    `;
    document.head.appendChild(style);

    // Initialize the application
    window.app = new BinanceSignalApp();
});

// Also initialize if DOM is already ready
if (document.readyState !== 'loading') {
    console.log('[NEW APP] DOM already ready, initializing application...');
    window.app = new BinanceSignalApp();
}
